<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>'s perfect cakes - Artisan Cakes in [Your City/Area]</title>
    <meta name="description" content="Discover exquisitely crafted cakes by <PERSON>. Browse our gallery and order online for your special occasions.">
    <link rel="stylesheet" href="style.css">
    <!-- Consider adding Favicon links here -->
</head>
<body>
    <header class="site-header">
        <div class="container header-container">
            <a href="index.html" class="brand-logo"><PERSON>'s perfect cakes</a>
            <nav class="main-nav" id="main-nav">
                <button class="nav-toggle" aria-label="Toggle navigation" aria-expanded="false">
                    <span class="hamburger"></span>
                </button>
                <ul>
                    <li><a href="index.html" class="active">Home</a></li>
                    <li><a href="gallery.html">Gallery & Shop</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                    <!-- Optional: Placeholder Cart Icon
                    <li><a href="#" class="cart-icon" aria-label="Shopping Cart">(0)</a></li>
                    -->
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="hero">
            <div class="container hero-content">
                <h1>Artisan Cakes Crafted with Passion</h1>
                <p class="subtitle">Discover unique, delicious cakes perfect for any celebration.</p>
                <a href="gallery.html" class="cta-button">Browse Our Cakes</a>
            </div>
            <!-- Background image is set via CSS -->
            <!-- TODO: Replace with a high-quality image of one of Antonio's stunning cakes -->
        </section>

        <!-- Featured Cakes Section -->
        <section class="featured-cakes section-padding">
            <div class="container">
                <h2>Featured Creations</h2>
                <div class="cake-grid">
                    <!-- Cake Item 1 -->
                    <div class="cake-card">
                        <img src="https://source.unsplash.com/random/400x300/?cake,chocolate" alt="Featured Cake 1 - Placeholder" loading="lazy">
                        <!-- TODO: Replace with actual cake image -->
                        <h3>[Featured Cake Name 1]</h3>
                        <p>[Short description of the featured cake...]</p>
                        <span class="price">[Price e.g., $50.00]</span>
                        <a href="gallery.html#cake1" class="button-secondary">View Details</a> <!-- Link to specific cake if possible -->
                    </div>
                    <!-- Cake Item 2 -->
                    <div class="cake-card">
                        <img src="https://source.unsplash.com/random/400x300/?cake,wedding" alt="Featured Cake 2 - Placeholder" loading="lazy">
                        <!-- TODO: Replace with actual cake image -->
                        <h3>[Featured Cake Name 2]</h3>
                        <p>[Short description of the featured cake...]</p>
                        <span class="price">[Price e.g., $65.00]</span>
                        <a href="gallery.html#cake2" class="button-secondary">View Details</a>
                    </div>
                    <!-- Cake Item 3 -->
                    <div class="cake-card">
                        <img src="https://source.unsplash.com/random/400x300/?cake,birthday" alt="Featured Cake 3 - Placeholder" loading="lazy">
                        <!-- TODO: Replace with actual cake image -->
                        <h3>[Featured Cake Name 3]</h3>
                        <p>[Short description of the featured cake...]</p>
                        <span class="price">[Price e.g., Starts at $45.00]</span>
                        <a href="gallery.html#cake3" class="button-secondary">View Details</a>
                    </div>
                </div>
                <div class="text-center top-margin">
                    <a href="gallery.html" class="cta-button">See All Cakes</a>
                </div>
            </div>
        </section>

        <!-- About Snippet Section -->
        <section class="about-snippet section-padding bg-light">
            <div class="container">
                <h2>Meet Antonio</h2>
                <img src="https://via.placeholder.com/150x150.png?text=Antonio" alt="Antonio - Placeholder" class="profile-pic-small">
                <!-- TODO: Replace with a photo of Antonio or the bakery -->
                <p>[Brief introduction about Antonio and his passion for baking. Mention experience or unique approach. E.g., Using only the finest ingredients, Antonio crafts each cake into a masterpiece...]</p>
                <a href="about.html" class="button-secondary">Learn More About Us</a>
            </div>
        </section>

         <!-- Call to Action Section -->
         <section class="cta-section section-padding">
             <div class="container text-center">
                 <h2>Ready for the Perfect Cake?</h2>
                 <p>[Encourage users to get in touch for custom orders or browse the selection.]</p>
                 <a href="contact.html" class="cta-button">Order Your Custom Cake</a>
                 <a href="gallery.html" class="button-secondary">Explore Gallery</a>
             </div>
         </section>

    </main>

    <footer class="site-footer">
        <div class="container">
            <p>&copy; <span id="current-year"></span> Antonio's perfect cakes. All Rights Reserved.</p>
            <!-- Optional: Add social media links here -->
            <!-- <ul class="social-links">
                <li><a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a></li>
                <li><a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a></li>
            </ul> -->
            <!-- TODO: Add real social media links -->
            <!-- Consider adding Font Awesome script if using icons: <script src="https://kit.fontawesome.com/[yourkitid].js" crossorigin="anonymous"></script> -->
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>