import os
import sys
import google.generativeai as genai

def main():
    try:
        api_key = os.environ.get('GEMINI_API_KEY')
        if not api_key:
            print("Error: GEMINI_API_KEY no encontrada en variables de entorno", file=sys.stderr)
            return 1
            
        print(f"Usando API Key: {api_key[:5]}...{api_key[-3:]}", file=sys.stderr)
        genai.configure(api_key=api_key)
        
        # Configuración del modelo
        model = genai.GenerativeModel(
            model_name="gemini-pro"
        )
        
        # Hacer una petición simple de prueba
        response = model.generate_content("Ho<PERSON>, por favor responde solo con la palabra 'FUNCIONA' para confirmar que la conexión es correcta.")
        
        print(f"Respuesta de Gemini: {response.text}")
        return 0
    except Exception as e:
        print(f"Error con Gemini: {str(e)}", file=sys.stderr)
        return 1

if __name__ == "__main__":
    sys.exit(main())
