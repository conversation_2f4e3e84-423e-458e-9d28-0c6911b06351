"""
Content generation service - Simplified and intelligent.
Uses Creative Genius for all content generation.
"""

import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)


class ContentGenerationService:
    """Simplified service that uses Creative Genius for all content generation."""
    
    def __init__(self):
        self.creative_genius = None
        self._initialize_creative_genius()
    
    def _initialize_creative_genius(self):
        """Initialize Creative Genius service."""
        try:
            from app.services.creative_genius_service import CreativeGeniusService
            self.creative_genius = CreativeGeniusService()
            logger.info("✅ Content Generation Service initialized with Creative Genius")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Creative Genius: {e}")
    
    async def generate_posts(self, user_context: Dict[str, Any], content_type: str = "educational", num_posts: int = 3) -> List[Dict[str, Any]]:
        """
        Generate multiple posts using Creative Genius.
        
        Args:
            user_context: User context (businessName, industry, etc.)
            content_type: Type of content (educational, motivational, balanced)
            num_posts: Number of posts to generate
            
        Returns:
            List of generated posts with visual hooks and content
        """
        if not self.creative_genius:
            logger.error("Creative Genius not available")
            return []
        
        posts = []
        
        for i in range(num_posts):
            try:
                # Generate creative breakthrough concept
                breakthrough = await self.creative_genius.create_breakthrough_content(user_context, content_type)
                
                # Generate post content using the breakthrough
                post_content = await self.generate_post_content(breakthrough, user_context)
                
                post = {
                    "visual_hook": breakthrough.hook,
                    "content": post_content,
                    "ideogram_prompt": breakthrough.ideogram_prompt,
                    "visual_concept": breakthrough.visual_concept,
                    "viral_score": breakthrough.viral_score
                }
                
                posts.append(post)
                logger.info(f"✅ Generated post {i+1}/{num_posts}")
                
            except Exception as e:
                logger.error(f"Error generating post {i+1}: {e}")
                continue
        
        return posts
    
    async def generate_post_content(self, creative_breakthrough, user_context: Dict[str, Any], platform: str = "Instagram") -> str:
        """
        Generate post content from Creative Genius breakthrough.
        
        Args:
            creative_breakthrough: CreativeBreakthrough object from Creative Genius
            user_context: User context information
            platform: Target platform
            
        Returns:
            Generated post content
        """
        if not self.creative_genius:
            return self._generate_simple_fallback(user_context)
        
        try:
            from app.utils.platform_utils import get_platform_character_limits
            from app.utils.content_utils import optimize_content_for_platform
            
            # Get platform limits
            char_limits = get_platform_character_limits(platform)
            
            # Generate intelligent content based on breakthrough
            content = await self._generate_intelligent_content(creative_breakthrough, user_context, char_limits)
            
            # Optimize for platform
            optimized_content = optimize_content_for_platform(content, platform, char_limits)
            
            return optimized_content
            
        except Exception as e:
            logger.error(f"Error generating post content: {e}")
            return self._generate_simple_fallback(user_context)
    
    async def _generate_intelligent_content(self, breakthrough, user_context: Dict[str, Any], char_limits: Dict[str, int]) -> str:
        """Generate intelligent content based on Creative Genius breakthrough."""
        try:
            import google.generativeai as genai
            from app.core.config import settings
            
            if not settings.GEMINI_API_KEY:
                return self._generate_simple_fallback(user_context)
            
            genai.configure(api_key=settings.GEMINI_API_KEY)
            model = genai.GenerativeModel('gemini-1.5-flash')
            
            business_name = user_context.get("businessName", "Business")
            industry = user_context.get("industry", "general")
            content_angle = breakthrough.content_angle
            hook = breakthrough.hook
            
            # Check if there are specific user topics/instructions
            user_topics = user_context.get("topics", [])
            user_specific_instructions = ""
            if user_topics and len(user_topics) > 0 and user_topics[0].strip():
                user_topic = user_topics[0]
                user_specific_instructions = f"""
INSTRUCCIONES ESPECÍFICAS DEL USUARIO: "{user_topic}"
IMPORTANTE: El contenido DEBE estar relacionado con este tema específico del usuario.
"""

            # Enhanced prompt that respects user instructions
            prompt = f"""
Crea un post de redes sociales {content_angle} para {business_name} en {industry}.

Negocio: {business_name}
Industria: {industry}
Tipo: {content_angle}
{user_specific_instructions}
Máximo {char_limits.get('optimal', 800)} caracteres en español.

Genera el contenido siguiendo las instrucciones específicas del usuario:
"""
            
            response = model.generate_content(prompt)
            
            if response and response.text:
                content = response.text.strip()
                
                # Remove hook if it appears in content
                if hook.lower() in content.lower():
                    content = content.replace(hook, "").strip()
                    content = " ".join(content.split())
                
                return content
            else:
                return self._generate_simple_fallback(user_context)
                
        except Exception as e:
            logger.error(f"Error generating intelligent content: {e}")
            return self._generate_simple_fallback(user_context)
    
    def _generate_simple_fallback(self, user_context: Dict[str, Any]) -> str:
        """Simple fallback content when everything fails."""
        business_name = user_context.get("businessName", "Nuestra marca")
        industry = user_context.get("industry", "general")
        
        return f"Descubre más sobre {industry} con {business_name}.\n\n¿Qué te gustaría saber? 💭\n\n#Contenido #{industry.title()}"

    # SIMILARITY FUNCTIONALITY MOVED TO DEDICATED SimilarPostsService
    # This keeps ContentGenerationService focused on its core responsibility


    # COMPATIBILITY METHODS - For backward compatibility with existing code

    async def generate_visual_hook_content(self, content_strategy: Dict[str, Any], brand_info: Dict[str, Any]) -> str:
        """
        COMPATIBILITY: Generate visual hook content.
        Redirects to Creative Genius for consistency.
        """
        logger.info("🔄 Using compatibility method - redirecting to Creative Genius")

        try:
            # Extract content type from strategy
            context_analysis = content_strategy.get("context_analysis", {})
            content_type = context_analysis.get("tipo_contenido", "educational")

            # Create user context from brand info
            user_context = {
                "businessName": brand_info.get("businessName", "Business"),
                "industry": brand_info.get("industry", "general"),
                "target_audience": brand_info.get("target_audience", "general audience")
            }

            # Generate breakthrough concept
            breakthrough = await self.creative_genius.create_breakthrough_content(user_context, content_type)

            return breakthrough.hook

        except Exception as e:
            logger.error(f"Error in compatibility visual hook generation: {e}")
            # Simple fallback
            topic = content_strategy.get("topic", "marketing digital")
            return f"Descubre {topic.title()}"

    async def generate_creative_genius_content(self, creative_concept, brand_info: Dict[str, Any], platform: str) -> str:
        """
        COMPATIBILITY: Generate content using Creative Genius concept.
        Redirects to the new generate_post_content method.
        """
        logger.info("🔄 Using compatibility method - redirecting to generate_post_content")

        user_context = {
            "businessName": brand_info.get("businessName", "Business"),
            "industry": brand_info.get("industry", "general")
        }

        return await self.generate_post_content(creative_concept, user_context, platform)

    # COMPATIBILITY METHODS REMOVED - Use SimilarPostsService instead
    # This keeps ContentGenerationService clean and focused
