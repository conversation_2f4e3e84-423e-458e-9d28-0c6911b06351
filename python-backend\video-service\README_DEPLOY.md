# Video Service Backend – Guía de Deploy y Entorno

## 1. Variables de entorno necesarias

Debes definir estas variables antes de arrancar el backend:

- `GEMINI_API_KEY` – Tu clave de API de Gemini (Google AI)
- `ELEVENLABS_API_KEY` – Tu clave de ElevenLabs
- `VIDEO_TEMP_DIR` – (Opcional) Carpeta temporal para videos

Puedes ponerlas en un archivo `.env` (local) o en el panel de variables de tu proveedor cloud (Railway, Render, etc.).

---

## 2. Instalación de dependencias

```
pip install -r requirements.txt
```

---

## 3. Arranque local

```
# Exporta las variables de entorno primero
export GEMINI_API_KEY=tu_clave
export ELEVENLABS_API_KEY=tu_clave

# Arranca el backend
uvicorn main:app --host 0.0.0.0 --port 8000 --reload --log-level debug
```

Abre [http://localhost:8000/docs](http://localhost:8000/docs) para ver la documentación interactiva.

---

## 4. Deploy en Cloud (ejemplo Railway)

1. Sube tu código a GitHub.
2. En Railway, crea un nuevo proyecto desde el repo.
3. Ve a **Variables** y pon:
   - `GEMINI_API_KEY`
   - `ELEVENLABS_API_KEY`
   - (Opcional) `VIDEO_TEMP_DIR`
4. Railway detectará automáticamente `requirements.txt` y levantará el backend.
5. Tu backend estará disponible en una URL pública (ej: `https://mi-backend.up.railway.app`).

---

## 5. Conectar el frontend

- Cambia las llamadas fetch/axios en el frontend para apuntar a la URL pública del backend (no a localhost).
- Usa variables de entorno en el frontend si es posible (`VITE_API_URL`, etc).

---

## 6. Troubleshooting

- Si el backend no arranca, revisa logs de Railway/Render y asegúrate de que las claves estén bien puestas.
- Si ves errores de red en el frontend, revisa que la URL del backend sea correcta y que no haya CORS bloqueando.

---

**¡Listo! Así tendrás el entorno preparado y funcionando donde sea: local, cloud, producción o desarrollo.**
