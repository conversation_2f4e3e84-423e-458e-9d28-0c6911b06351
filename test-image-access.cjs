const http = require('http');

const imagesToTest = [
  '/Agent-Marketplace.png',
  '/Editor.png',
  '/Emma.png',
  '/Visual-Studio.png',
  '/Herramientas.png',
  '/Ads-Central.png'
];

const testImageAccess = (imagePath) => {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3003,
      path: imagePath,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      console.log(`${imagePath}: Status ${res.statusCode} - ${res.statusMessage}`);
      console.log(`Content-Type: ${res.headers['content-type']}`);
      console.log(`Content-Length: ${res.headers['content-length']}`);
      
      resolve({
        path: imagePath,
        status: res.statusCode,
        contentType: res.headers['content-type'],
        contentLength: res.headers['content-length'],
        success: res.statusCode === 200
      });
    });

    req.on('error', (err) => {
      console.error(`Error testing ${imagePath}:`, err.message);
      resolve({
        path: imagePath,
        status: 'ERROR',
        error: err.message,
        success: false
      });
    });

    req.end();
  });
};

const runTests = async () => {
  console.log('🧪 Testing image accessibility on http://localhost:3003\n');
  
  const results = [];
  
  for (const imagePath of imagesToTest) {
    const result = await testImageAccess(imagePath);
    results.push(result);
    console.log('---');
  }
  
  console.log('\n📊 Summary:');
  console.log(`✅ Successful: ${results.filter(r => r.success).length}`);
  console.log(`❌ Failed: ${results.filter(r => !r.success).length}`);
  
  const failed = results.filter(r => !r.success);
  if (failed.length > 0) {
    console.log('\n❌ Failed images:');
    failed.forEach(f => {
      console.log(`  - ${f.path}: ${f.status} ${f.error || ''}`);
    });
  }
  
  const successful = results.filter(r => r.success);
  if (successful.length > 0) {
    console.log('\n✅ Successful images:');
    successful.forEach(s => {
      console.log(`  - ${s.path}: ${s.contentType}, ${s.contentLength} bytes`);
    });
  }
};

runTests().catch(console.error);
