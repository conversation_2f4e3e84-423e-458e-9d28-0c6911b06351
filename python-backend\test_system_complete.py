#!/usr/bin/env python3
"""
Complete system test for Emma Studio Agent System.

This script tests the entire system end-to-end to verify everything is working.
"""

import asyncio
import json
import requests
import time
from typing import Dict, Any

def test_backend_health():
    """Test if the backend is healthy and responding."""
    print("🔍 Testing backend health...")

    try:
        response = requests.get("http://localhost:8000/api/v1/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend health check passed")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend health check failed: {str(e)}")
        return False

def test_agent_endpoints():
    """Test agent-related endpoints."""
    print("\n🤖 Testing agent endpoints...")

    try:
        # Test get available agents
        response = requests.get("http://localhost:8000/api/v1/agents", timeout=10)
        if response.status_code == 200:
            agents = response.json()
            print(f"✅ Available agents endpoint working: {len(agents)} agents found")
            for agent in agents:
                print(f"  - {agent['name']}: {agent['role']}")
        else:
            print(f"❌ Agents endpoint failed: {response.status_code}")
            return False

        # Test agent chat
        chat_data = {
            "agent_id": "emma",
            "message": "Hello, can you help me test the system?",
            "context": {}
        }

        response = requests.post(
            "http://localhost:8000/api/v1/agent/chat",
            json=chat_data,
            timeout=60
        )

        if response.status_code == 200:
            chat_response = response.json()
            print(f"✅ Agent chat working: {chat_response['response'][:100]}...")
            return True
        else:
            print(f"❌ Agent chat failed: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Agent endpoints test failed: {str(e)}")
        return False

def test_crew_workflow():
    """Test crew workflow execution."""
    print("\n👥 Testing crew workflow...")

    try:
        workflow_data = {
            "crew_id": "test_crew",
            "prompt": "Create a brief marketing strategy for AI agents",
            "inputs": {"topic": "AI agents", "target_audience": "developers"},
            "context": {"test": True}
        }

        response = requests.post(
            "http://localhost:8000/api/v1/crew/run",
            json=workflow_data,
            timeout=60
        )

        if response.status_code == 200:
            workflow_response = response.json()
            print(f"✅ Crew workflow working: {workflow_response['status']}")
            print(f"  Result: {workflow_response['result'][:100]}...")
            print(f"  Reasoning steps: {len(workflow_response.get('reasoning_trace', []))}")
            return True
        else:
            print(f"❌ Crew workflow failed: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Crew workflow test failed: {str(e)}")
        return False

def test_frontend_accessibility():
    """Test if the frontend is accessible."""
    print("\n🌐 Testing frontend accessibility...")

    try:
        response = requests.get("http://localhost:3002", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is accessible")
            return True
        else:
            print(f"❌ Frontend accessibility failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend accessibility test failed: {str(e)}")
        return False

def test_websocket_endpoint():
    """Test WebSocket endpoint availability."""
    print("\n🔌 Testing WebSocket endpoint...")

    try:
        # We can't easily test WebSocket connection without additional libraries
        # But we can check if the endpoint exists by trying to connect
        import socket

        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 8000))
        sock.close()

        if result == 0:
            print("✅ WebSocket endpoint is accessible")
            return True
        else:
            print("❌ WebSocket endpoint not accessible")
            return False

    except Exception as e:
        print(f"❌ WebSocket test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Complete System Test for Emma Studio")
    print("=" * 60)

    tests = [
        ("Backend Health", test_backend_health),
        ("Agent Endpoints", test_agent_endpoints),
        ("Crew Workflow", test_crew_workflow),
        ("Frontend Accessibility", test_frontend_accessibility),
        ("WebSocket Endpoint", test_websocket_endpoint),
    ]

    results = {}

    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 40)

        start_time = time.time()
        result = test_func()
        end_time = time.time()

        results[test_name] = {
            "passed": result,
            "duration": round(end_time - start_time, 2)
        }

        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"Result: {status} ({results[test_name]['duration']}s)")

    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)

    passed_tests = sum(1 for r in results.values() if r["passed"])
    total_tests = len(results)

    for test_name, result in results.items():
        status = "✅ PASSED" if result["passed"] else "❌ FAILED"
        print(f"{test_name:.<30} {status} ({result['duration']}s)")

    print("-" * 60)
    print(f"Total: {passed_tests}/{total_tests} tests passed")

    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED! The system is working correctly!")
        print("\n🌟 Your Emma Studio Agent System is ready to use:")
        print("   • Backend: http://localhost:8000")
        print("   • Frontend: http://localhost:3002")
        print("   • Agent UI: http://localhost:3002/vibe-agents")
        print("   • API Docs: http://localhost:8000/docs")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} test(s) failed. Please check the issues above.")

        if not results.get("Backend Health", {}).get("passed"):
            print("\n💡 Backend not running? Start it with:")
            print("   cd backend && poetry run uvicorn app.main:app --reload")

        if not results.get("Frontend Accessibility", {}).get("passed"):
            print("\n💡 Frontend not running? Start it with:")
            print("   cd client && npm run dev")

if __name__ == "__main__":
    main()
