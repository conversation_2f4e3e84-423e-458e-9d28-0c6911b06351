import os
import uuid
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from pydantic import BaseModel
from diffusers import VideoDiffusionPipeline
from moviepy.editor import (
    ImageSequenceClip, AudioFileClip,
    CompositeVideoClip, TextClip, ImageClip
)
from elevenlabs import set_api_key, generate
import google.generativeai as genai
import torch
from typing import Dict
from fastapi.middleware.cors import CORSMiddleware

# Model definitions
class Subtitle(BaseModel):
    text: str
    start: float
    end: float
    position: str = "bottom"

class OverlayImage(BaseModel):
    url: str
    start: float
    end: float
    position: str = "center"

class VideoRequest(BaseModel):
    prompt: str
    negative_prompt: str = ""
    num_frames: int = 32
    fps: int = 15
    voice: str = "Bella"
    narration_text: str = ""
    subtitles: list[Subtitle] = []
    overlays: list[OverlayImage] = []

class ScriptRequest(BaseModel):
    topic: str
    max_tokens: int = 300

class ScriptResponse(BaseModel):
    script: str

# App setup
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")
ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY", "")
VIDEO_MODEL_ID = "facebook/video-diffusion-checkpoint"

temp_dir = os.getenv("VIDEO_TEMP_DIR", "temp_videos")
os.makedirs(temp_dir, exist_ok=True)

# Validación de dependencias críticas en arranque
missing = []
if not GEMINI_API_KEY:
    missing.append("GEMINI_API_KEY")
if not ELEVENLABS_API_KEY:
    missing.append("ELEVENLABS_API_KEY")
try:
    from diffusers import VideoDiffusionPipeline
    import torch
    _ = VideoDiffusionPipeline.from_pretrained(VIDEO_MODEL_ID)
except Exception as e:
    missing.append(f"Modelo Diffusers {VIDEO_MODEL_ID}: {e}")
if missing:
    raise RuntimeError(f"Dependencias críticas faltantes o mal configuradas: {', '.join(missing)}")

set_api_key(ELEVENLABS_API_KEY)
genai.configure(api_key=GEMINI_API_KEY)

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Puedes restringir esto a tu dominio frontend en producción
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Load diffusion pipeline (solo si hay GPU disponible)
diffusion_pipe = VideoDiffusionPipeline.from_pretrained(VIDEO_MODEL_ID).to("cuda" if torch.cuda.is_available() else "cpu")

# --- Job state in-memory ---
video_jobs: Dict[str, Dict] = {}

# Helper: generate a unique job_id
import uuid

def launch_video_job(req: VideoRequest, job_id: str):
    try:
        video_jobs[job_id]["status"] = "processing"
        # Generate frames
        result = diffusion_pipe(
            req.prompt,
            negative_prompt=req.negative_prompt,
            num_frames=req.num_frames
        )
        frames = result.frames  # list of PIL.Image
        clip = ImageSequenceClip(frames, fps=req.fps)
        # Generate audio via ElevenLabs
        narration = req.narration_text or req.prompt
        audio_bytes = generate(text=narration, voice=req.voice)
        audio_path = os.path.join(temp_dir, f"aud_{uuid.uuid4().hex}.mp3")
        with open(audio_path, "wb") as f:
            f.write(audio_bytes)
        audio_clip = AudioFileClip(audio_path)
        clip = clip.set_audio(audio_clip)
        # Add subtitles and overlays
        extras = [clip]
        for sub in req.subtitles:
            txt = (TextClip(sub.text, fontsize=24, color="white", font="Arial")
                   .set_start(sub.start)
                   .set_end(sub.end)
                   .set_position(sub.position))
            extras.append(txt)
        for over in req.overlays:
            img_clip = (ImageClip(over.url)
                        .set_start(over.start)
                        .set_end(over.end)
                        .set_position(over.position)
                        .resize(height=200))
            extras.append(img_clip)
        # Compose final video
        final_clip = CompositeVideoClip(extras)
        video_fname = f"vid_{uuid.uuid4().hex}.mp4"
        video_path = os.path.join(temp_dir, video_fname)
        final_clip.write_videofile(
            video_path,
            codec="libx264",
            audio_codec="aac",
            fps=req.fps
        )
        video_jobs[job_id]["status"] = "completed"
        video_jobs[job_id]["outputUrl"] = f"/temp_videos/{video_fname}"
        video_jobs[job_id]["output_path"] = video_path
    except Exception as e:
        video_jobs[job_id]["status"] = "failed"
        video_jobs[job_id]["error"] = str(e)

@app.post("/api/video-gen")
async def api_video_gen(req: VideoRequest, background_tasks: BackgroundTasks):
    job_id = str(uuid.uuid4())
    video_jobs[job_id] = {"status": "pending"}
    background_tasks.add_task(launch_video_job, req, job_id)
    status_url = f"/api/video/status/{job_id}"
    return {"success": True, "job_id": job_id, "statusUrl": status_url}

@app.get("/api/video/status/{job_id}")
async def api_video_status(job_id: str):
    job = video_jobs.get(job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    return job

@app.get("/api/video/list")
async def api_video_list():
    # Simple: list files in temp_videos
    files = []
    for fname in os.listdir(temp_dir):
        if fname.endswith(".mp4"):
            files.append({"filename": fname, "url": f"/temp_videos/{fname}"})
    return {"videos": files}

@app.delete("/api/video/delete/{filename}")
async def api_video_delete(filename: str):
    path = os.path.join(temp_dir, filename)
    if not os.path.exists(path):
        raise HTTPException(status_code=404, detail="Not found")
    os.remove(path)
    return {"success": True}

@app.post("/api/video/script", response_model=ScriptResponse)
async def api_video_script(req: ScriptRequest):
    # Usa Gemini para generar guion
    if not GEMINI_API_KEY:
        raise HTTPException(status_code=500, detail="GEMINI_API_KEY no configurada")
    try:
        model = genai.GenerativeModel("gemini-pro")
        prompt = f"Eres un guionista profesional de videos cortos. Escribe un guion profesional, conciso y atractivo para un video sobre: {req.topic}"
        response = model.generate_content(prompt, generation_config={"max_output_tokens": req.max_tokens})
        script = response.text.strip()
        return {"script": script}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error con Gemini: {e}")

@app.post("/generate_video")
async def generate_video(req: VideoRequest):
    try:
        # Generate frames
        result = diffusion_pipe(
            req.prompt,
            negative_prompt=req.negative_prompt,
            num_frames=req.num_frames
        )
        frames = result.frames  # list of PIL.Image

        # Create base clip
        clip = ImageSequenceClip(frames, fps=req.fps)

        # Generate audio via ElevenLabs
        narration = req.narration_text or req.prompt
        audio_bytes = generate(text=narration, voice=req.voice)
        audio_path = os.path.join(temp_dir, f"aud_{uuid.uuid4().hex}.mp3")
        with open(audio_path, "wb") as f:
            f.write(audio_bytes)
        audio_clip = AudioFileClip(audio_path)
        clip = clip.set_audio(audio_clip)

        # Add subtitles and overlays
        extras = [clip]
        for sub in req.subtitles:
            txt = (TextClip(sub.text, fontsize=24, color="white", font="Arial")
                   .set_start(sub.start)
                   .set_end(sub.end)
                   .set_position(sub.position))
            extras.append(txt)
        for over in req.overlays:
            img_clip = (ImageClip(over.url)
                        .set_start(over.start)
                        .set_end(over.end)
                        .set_position(over.position)
                        .resize(height=200))
            extras.append(img_clip)

        # Compose final video
        final_clip = CompositeVideoClip(extras)
        video_fname = f"vid_{uuid.uuid4().hex}.mp4"
        video_path = os.path.join(temp_dir, video_fname)
        final_clip.write_videofile(
            video_path,
            codec="libx264",
            audio_codec="aac",
            fps=req.fps
        )

        return {"url": f"/temp_videos/{video_fname}"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Endpoint para generación automática de guion (Gemini)
@app.post("/generate_script", response_model=ScriptResponse)
async def generate_script(req: ScriptRequest):
    if not GEMINI_API_KEY:
        raise HTTPException(status_code=500, detail="GEMINI_API_KEY no configurada")
    try:
        model = genai.GenerativeModel("gemini-pro")
        prompt = f"Eres un guionista profesional de videos cortos. Escribe un guion profesional, conciso y atractivo para un video sobre: {req.topic}"
        response = model.generate_content(prompt, generation_config={"max_output_tokens": req.max_tokens})
        script = response.text.strip()
        return {"script": script}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error con Gemini: {e}")

@app.get("/temp_videos/{filename}")
async def serve_video(filename: str):
    file_path = os.path.join(temp_dir, filename)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Not found")
    return FileResponse(file_path, media_type="video/mp4")
