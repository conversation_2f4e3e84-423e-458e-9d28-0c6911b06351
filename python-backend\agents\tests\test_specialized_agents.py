"""
Tests for the specialized agent implementations
"""

import unittest
import asyncio
from unittest.mock import MagicMock, patch

from agents import (
    AgentContext,
    AgentAction,
    ActionResult,
    ContextType,
    AgentTask,
    LL<PERSON>rovider
)
from agents.specialized import EmmaAgent, SEOAgent, ContentAgent

# Create a mock LLM provider for testing
class Mock<PERSON><PERSON>rovider(LLMProvider):
    async def generate(self, prompt, **kwargs):
        return f"Mock response for: {prompt[:50]}..."

class TestEmmaAgent(unittest.TestCase):
    """Test cases for the EmmaAgent class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.llm_provider = MockLLMProvider()
        self.agent = EmmaAgent("emma", "Emma", self.llm_provider)
    
    def test_initialization(self):
        """Test agent initialization."""
        self.assertEqual(self.agent.id, "emma")
        self.assertEqual(self.agent.name, "Emma")
        self.assertEqual(self.agent.llm_provider, self.llm_provider)
    
    def test_get_next_action(self):
        """Test the get_next_action method."""
        # Create a task
        task = AgentTask(
            id="task1",
            description="test task",
            priority=5
        )
        
        # Create a context
        context = AgentContext(
            context_type=ContextType.TASK,
            data={"task": task}
        )
        
        # Get the next action
        action = asyncio.run(self.agent.get_next_action(context))
        
        # Check the action
        self.assertEqual(action.type, "PROCESS")
        self.assertEqual(action.content, "test task")
        self.assertEqual(action.metadata["task_id"], "task1")
    
    def test_execute_action(self):
        """Test the execute_action method."""
        # Create an action
        action = AgentAction(
            action_type="PROCESS",
            content="test task",
            metadata={"task_id": "task1"}
        )
        
        # Execute the action
        result = asyncio.run(self.agent.execute_action(action))
        
        # Check the result
        self.assertTrue(result.success)
        self.assertIsNotNone(result.result)
        self.assertEqual(result.metadata["action_type"], "PROCESS")

class TestSEOAgent(unittest.TestCase):
    """Test cases for the SEOAgent class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.llm_provider = MockLLMProvider()
        self.agent = SEOAgent("seo", "SEO Specialist", self.llm_provider)
    
    def test_initialization(self):
        """Test agent initialization."""
        self.assertEqual(self.agent.id, "seo")
        self.assertEqual(self.agent.name, "SEO Specialist")
        self.assertEqual(self.agent.llm_provider, self.llm_provider)
        self.assertIn("keyword_research", self.agent.agent_identity.capabilities)
    
    def test_get_next_action(self):
        """Test the get_next_action method."""
        # Create a task
        task = AgentTask(
            id="task1",
            description="analyze SEO for this content",
            priority=5
        )
        
        # Create a context
        context = AgentContext(
            context_type=ContextType.TASK,
            data={"task": task}
        )
        
        # Get the next action
        action = asyncio.run(self.agent.get_next_action(context))
        
        # Check the action
        self.assertEqual(action.type, "PROCESS")
        self.assertEqual(action.content, "analyze SEO for this content")
        self.assertEqual(action.metadata["task_id"], "task1")
        self.assertTrue(action.metadata["seo_mode"])

class TestContentAgent(unittest.TestCase):
    """Test cases for the ContentAgent class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.llm_provider = MockLLMProvider()
        self.agent = ContentAgent("content", "Content Creator", self.llm_provider)
    
    def test_initialization(self):
        """Test agent initialization."""
        self.assertEqual(self.agent.id, "content")
        self.assertEqual(self.agent.name, "Content Creator")
        self.assertEqual(self.agent.llm_provider, self.llm_provider)
        self.assertIn("blog_writing", self.agent.agent_identity.capabilities)
    
    def test_get_next_action(self):
        """Test the get_next_action method."""
        # Create a task
        task = AgentTask(
            id="task1",
            description="write a blog post about AI",
            priority=5
        )
        
        # Create a context
        context = AgentContext(
            context_type=ContextType.TASK,
            data={"task": task}
        )
        
        # Get the next action
        action = asyncio.run(self.agent.get_next_action(context))
        
        # Check the action
        self.assertEqual(action.type, "PROCESS")
        self.assertEqual(action.content, "write a blog post about AI")
        self.assertEqual(action.metadata["task_id"], "task1")
        self.assertTrue(action.metadata["content_mode"])
    
    @patch.object(ContentAgent, '_process_content_task')
    def test_execute_action(self, mock_process_content_task):
        """Test the execute_action method."""
        # Mock the _process_content_task method
        mock_process_content_task.return_value = asyncio.Future()
        mock_process_content_task.return_value.set_result(
            ActionResult(
                success=True,
                result="Test content",
                metadata={"action_type": "COMPLETE", "content_created": True}
            )
        )
        
        # Create an action
        action = AgentAction(
            action_type="PROCESS",
            content="write a blog post about AI",
            metadata={"task_id": "task1", "content_mode": True}
        )
        
        # Execute the action
        result = asyncio.run(self.agent.execute_action(action))
        
        # Check the result
        self.assertTrue(result.success)
        self.assertEqual(result.result, "Test content")
        self.assertEqual(result.metadata["action_type"], "COMPLETE")
        self.assertTrue(result.metadata["content_created"])

if __name__ == "__main__":
    unittest.main()
