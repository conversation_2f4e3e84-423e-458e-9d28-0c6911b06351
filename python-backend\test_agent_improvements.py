#!/usr/bin/env python3
"""
Test script for the enhanced agent system improvements.

This script tests:
1. Reasoning trace collection and display
2. WebSocket real-time communication
3. Error handling and recovery
4. Agent orchestration improvements
"""

import asyncio
import json
import time
import uuid
from typing import Dict, Any

# Test the agent service directly
async def test_agent_service():
    """Test the agent service functionality."""
    print("Testing Agent Service...")
    
    try:
        from backend.app.services.agent_service import (
            chat_with_agent, 
            chat_with_agent_stream,
            get_available_agents,
            run_agent_workflow
        )
        from backend.app.schemas.crew import AgentChatRequest, CrewRunRequest
        
        # Test 1: Get available agents
        print("\n1. Testing get_available_agents...")
        agents = get_available_agents()
        print(f"Available agents: {len(agents)}")
        for agent in agents:
            print(f"  - {agent['name']} ({agent['id']}): {agent['role']}")
        
        # Test 2: Chat with agent
        print("\n2. Testing chat_with_agent...")
        chat_request = AgentChatRequest(
            agent_id="emma",
            message="Hello, can you help me with content creation?",
            context={"test": True}
        )
        
        response = await chat_with_agent(chat_request)
        print(f"Chat response: {response.response[:100]}...")
        print(f"Metadata: {response.metadata}")
        
        # Test 3: Streaming chat
        print("\n3. Testing chat_with_agent_stream...")
        stream_chunks = []
        async for chunk in chat_with_agent_stream(chat_request):
            stream_chunks.append(chunk)
            if len(chunk) > 0:
                print(f"Chunk: {chunk[:50]}...")
        
        print(f"Total streaming chunks: {len(stream_chunks)}")
        
        # Test 4: Workflow execution
        print("\n4. Testing run_agent_workflow...")
        workflow_request = CrewRunRequest(
            crew_id="test_crew",
            prompt="Create SEO-optimized content about AI agents",
            inputs={"topic": "AI agents", "target_audience": "developers"}
        )
        
        workflow_response = await run_agent_workflow(workflow_request)
        print(f"Workflow status: {workflow_response.status}")
        print(f"Workflow result: {workflow_response.result[:100] if workflow_response.result else 'No result'}...")
        print(f"Reasoning trace steps: {len(workflow_response.reasoning_trace)}")
        
        for i, step in enumerate(workflow_response.reasoning_trace[:3]):  # Show first 3 steps
            print(f"  Step {i+1}: {step.get('type', 'unknown')} - {step.get('content', '')[:50]}...")
        
        print("✅ Agent service tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Agent service test failed: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_websocket_functionality():
    """Test WebSocket functionality."""
    print("\nTesting WebSocket Functionality...")
    
    try:
        import websockets
        import json
        
        # Test WebSocket connection
        uri = "ws://localhost:8000/api/v1/ws"
        
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connection established")
            
            # Send registration message
            registration_msg = {
                "type": "register_session",
                "content": {
                    "session_id": str(uuid.uuid4())
                }
            }
            
            await websocket.send(json.dumps(registration_msg))
            response = await websocket.recv()
            reg_response = json.loads(response)
            print(f"Registration response: {reg_response['type']}")
            
            # Send agent request
            agent_request = {
                "type": "agent_request",
                "content": {
                    "message": "Test message for reasoning trace"
                },
                "session_id": registration_msg["content"]["session_id"],
                "agent_id": "emma",
                "request_id": str(uuid.uuid4())
            }
            
            await websocket.send(json.dumps(agent_request))
            
            # Collect responses
            responses = []
            timeout_count = 0
            max_timeout = 10  # 10 seconds timeout
            
            while timeout_count < max_timeout:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    response_data = json.loads(response)
                    responses.append(response_data)
                    print(f"Received: {response_data['type']}")
                    
                    if response_data['type'] == 'agent_response_complete':
                        print("✅ Complete response received")
                        if 'reasoning_trace' in response_data['content']:
                            trace = response_data['content']['reasoning_trace']
                            print(f"Reasoning trace steps: {len(trace)}")
                        break
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    continue
            
            print(f"Total WebSocket responses: {len(responses)}")
            print("✅ WebSocket functionality test completed!")
            
    except Exception as e:
        print(f"❌ WebSocket test failed: {str(e)}")
        print("Note: Make sure the backend server is running on localhost:8000")

async def test_error_handling():
    """Test error handling and recovery."""
    print("\nTesting Error Handling...")
    
    try:
        from backend.app.services.agent_service import chat_with_agent
        from backend.app.schemas.crew import AgentChatRequest
        
        # Test 1: Invalid agent ID
        print("1. Testing invalid agent ID...")
        try:
            invalid_request = AgentChatRequest(
                agent_id="nonexistent_agent",
                message="Test message",
                context={}
            )
            response = await chat_with_agent(invalid_request)
            print(f"Response: {response.response}")
        except Exception as e:
            print(f"Expected error caught: {str(e)}")
        
        # Test 2: Empty message
        print("2. Testing empty message...")
        try:
            empty_request = AgentChatRequest(
                agent_id="emma",
                message="",
                context={}
            )
            response = await chat_with_agent(empty_request)
            print(f"Response: {response.response}")
        except Exception as e:
            print(f"Expected error caught: {str(e)}")
        
        print("✅ Error handling tests completed!")
        
    except Exception as e:
        print(f"❌ Error handling test failed: {str(e)}")

def test_frontend_integration():
    """Test frontend integration points."""
    print("\nTesting Frontend Integration...")
    
    try:
        # Test reasoning trace display structure
        sample_reasoning_trace = [
            {
                "timestamp": int(time.time() * 1000),
                "type": "reasoning",
                "agent": "emma",
                "agent_name": "Emma",
                "content": "I'm analyzing the user's request to understand what they need."
            },
            {
                "timestamp": int(time.time() * 1000) + 1000,
                "type": "tool_usage",
                "agent": "emma",
                "agent_name": "Emma",
                "content": "Using content generation tool to create response."
            },
            {
                "timestamp": int(time.time() * 1000) + 2000,
                "type": "completion",
                "agent": "emma",
                "agent_name": "Emma",
                "content": "Task completed successfully."
            }
        ]
        
        print(f"Sample reasoning trace with {len(sample_reasoning_trace)} steps:")
        for i, step in enumerate(sample_reasoning_trace):
            print(f"  Step {i+1}: {step['type']} - {step['content']}")
        
        # Test WebSocket message structure
        sample_ws_message = {
            "type": "agent_response_complete",
            "content": {
                "request_id": str(uuid.uuid4()),
                "agent_id": "emma",
                "message": "Response complete",
                "full_response": "This is a complete response from the agent.",
                "reasoning_trace": sample_reasoning_trace
            }
        }
        
        print(f"\nSample WebSocket message structure: {json.dumps(sample_ws_message, indent=2)[:200]}...")
        
        print("✅ Frontend integration tests completed!")
        
    except Exception as e:
        print(f"❌ Frontend integration test failed: {str(e)}")

async def main():
    """Run all tests."""
    print("🚀 Starting Agent System Improvement Tests")
    print("=" * 50)
    
    # Run tests
    await test_agent_service()
    await test_websocket_functionality()
    await test_error_handling()
    test_frontend_integration()
    
    print("\n" + "=" * 50)
    print("🎉 All tests completed!")
    print("\nTo test the full system:")
    print("1. Start the backend: cd backend && poetry run uvicorn app.main:app --reload")
    print("2. Start the frontend: cd client && npm run dev")
    print("3. Navigate to http://localhost:5173/emma-ai")
    print("4. Test the agent system with reasoning trace display")

if __name__ == "__main__":
    asyncio.run(main())
