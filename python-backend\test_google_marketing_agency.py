#!/usr/bin/env python3
"""
Test script for Google Marketing Agency integration
Verifies that all components are working correctly
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

try:
    from app.services.google_marketing_agency_service import google_marketing_service
    print("✅ Google Marketing Agency service imported successfully")
except ImportError as e:
    print(f"❌ Failed to import Google Marketing Agency service: {e}")
    sys.exit(1)

async def test_service_status():
    """Test the service status"""
    print("\n🔍 Testing service status...")
    
    status = google_marketing_service.get_agent_status()
    print(f"Service available: {status['available']}")
    print(f"Message: {status['message']}")
    
    for agent_name, agent_status in status['agents'].items():
        print(f"  - {agent_name}: {agent_status}")
    
    return status['available']

async def test_domain_suggestions():
    """Test domain suggestions agent"""
    print("\n🌐 Testing domain suggestions...")
    
    if not google_marketing_service.is_available:
        print("❌ Service not available, skipping test")
        return
    
    try:
        result = await google_marketing_service.run_domain_suggestions(
            keywords="organic bakery artisan bread",
            user_id="test_user"
        )
        
        if result["success"]:
            print("✅ Domain suggestions generated successfully")
            print(f"Response preview: {result['domains'][:200]}...")
        else:
            print(f"❌ Domain suggestions failed: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Exception in domain suggestions: {e}")

async def test_website_creation():
    """Test website creation agent"""
    print("\n🏗️ Testing website creation...")
    
    if not google_marketing_service.is_available:
        print("❌ Service not available, skipping test")
        return
    
    try:
        domain_info = """
        Domain Name: organicbakery.com
        Brand/Project Name: Organic Artisan Bakery
        Primary Goal/Purpose: Showcase artisan bread and attract local customers
        Key Services/Products: Sourdough bread, pastries, custom cakes
        Target Audience: Local residents and food enthusiasts
        Style Preferences: Rustic and warm, using earth tones
        """
        
        result = await google_marketing_service.run_website_creation(
            domain_info=domain_info,
            user_id="test_user"
        )
        
        if result["success"]:
            print("✅ Website creation completed successfully")
            print(f"Response preview: {result['website_code'][:200]}...")
        else:
            print(f"❌ Website creation failed: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Exception in website creation: {e}")

async def test_marketing_strategy():
    """Test marketing strategy agent"""
    print("\n📈 Testing marketing strategy...")
    
    if not google_marketing_service.is_available:
        print("❌ Service not available, skipping test")
        return
    
    try:
        brand_info = """
        Brand/Project Name: Organic Artisan Bakery
        Product/Service Details: Handcrafted sourdough bread, pastries, and custom cakes using organic ingredients
        Primary Marketing Goals: Increase local awareness and drive foot traffic to bakery
        Target Audience Profile: Health-conscious local residents, food enthusiasts, and families
        Budget Constraints: $2,000/month for marketing activities
        Current Marketing Efforts: Word of mouth and local farmers market presence
        Competitive Landscape: 3 other bakeries in the area, but none focus on organic ingredients
        Brand Voice/Tone: Warm, authentic, and community-focused
        Preferred Channels: Instagram, local community events, email newsletter
        Key Performance Indicators: Foot traffic, social media engagement, email subscribers
        Timeline: 6-month marketing campaign
        Geographic Focus: 5-mile radius around bakery location
        """
        
        result = await google_marketing_service.run_marketing_strategy(
            brand_info=brand_info,
            user_id="test_user"
        )
        
        if result["success"]:
            print("✅ Marketing strategy generated successfully")
            print(f"Response preview: {result['strategy'][:200]}...")
        else:
            print(f"❌ Marketing strategy failed: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Exception in marketing strategy: {e}")

async def test_logo_generation():
    """Test logo generation agent"""
    print("\n🎨 Testing logo generation...")
    
    if not google_marketing_service.is_available:
        print("❌ Service not available, skipping test")
        return
    
    try:
        brand_info = """
        Create a logo for: Organic Artisan Bakery
        Domain: organicbakery.com
        Brand Description: Handcrafted organic bread and pastries with traditional methods
        Style Preferences: Rustic, warm, incorporating wheat or bread elements
        """
        
        result = await google_marketing_service.run_logo_generation(
            brand_info=brand_info,
            user_id="test_user"
        )
        
        if result["success"]:
            print("✅ Logo generation completed successfully")
            print(f"Response preview: {result['response'][:200]}...")
            print(f"Artifacts count: {len(result.get('artifacts', []))}")
        else:
            print(f"❌ Logo generation failed: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Exception in logo generation: {e}")

async def test_coordinator_workflow():
    """Test the coordinator workflow"""
    print("\n🎯 Testing coordinator workflow...")
    
    if not google_marketing_service.is_available:
        print("❌ Service not available, skipping test")
        return
    
    try:
        user_input = """
        Hello! I want to create a complete online presence for my organic artisan bakery. 
        I specialize in handcrafted sourdough bread and pastries using organic ingredients. 
        My target audience is health-conscious local residents and food enthusiasts. 
        Can you help me with everything from finding a domain name to creating a marketing strategy?
        """
        
        result = await google_marketing_service.run_coordinator_workflow(
            user_input=user_input,
            user_id="test_user"
        )
        
        if result["success"]:
            print("✅ Coordinator workflow completed successfully")
            print(f"Response preview: {result['response'][:300]}...")
        else:
            print(f"❌ Coordinator workflow failed: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Exception in coordinator workflow: {e}")

async def main():
    """Run all tests"""
    print("🚀 Google Marketing Agency Integration Test")
    print("=" * 50)
    
    # Test service status
    service_available = await test_service_status()
    
    if not service_available:
        print("\n⚠️  Google Marketing Agency service is not available.")
        print("This is expected if Google Cloud is not configured yet.")
        print("Please follow the setup instructions in GOOGLE_MARKETING_AGENCY_SETUP.md")
        return
    
    # Test individual agents
    await test_domain_suggestions()
    await test_website_creation()
    await test_marketing_strategy()
    await test_logo_generation()
    
    # Test coordinator workflow
    await test_coordinator_workflow()
    
    print("\n🎉 All tests completed!")
    print("Check the results above to verify functionality.")

if __name__ == "__main__":
    asyncio.run(main())
