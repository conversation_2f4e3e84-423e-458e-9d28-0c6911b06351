#!/usr/bin/env python3
"""
Debug script to test Visual Complexity Analyzer image upload step by step
"""

import asyncio
import logging
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.supabase import SupabaseService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_image_upload():
    """Debug the image upload process step by step"""
    
    print("🔍 DEBUG: Visual Complexity Analyzer Image Upload")
    print("=" * 60)
    
    try:
        # Step 1: Create service instance
        print("\n📋 Step 1: Creating Supabase service instance...")
        service = SupabaseService()
        print("✅ Service instance created")
        
        # Step 2: Create test image content
        print("\n📋 Step 2: Creating test image content...")
        # Create a simple test image (PNG header + minimal data)
        test_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
        
        print(f"✅ Test image created: {len(test_content)} bytes")
        
        # Step 3: Test parameters
        user_id = "test-debug-user-123"
        filename = "debug-test.png"
        content_type = "image/png"
        
        print(f"📊 Test parameters:")
        print(f"   - user_id: {user_id}")
        print(f"   - filename: {filename}")
        print(f"   - content_type: {content_type}")
        print(f"   - size: {len(test_content)} bytes")
        
        # Step 4: Test upload WITHOUT authentication first
        print("\n📋 Step 4: Testing upload WITHOUT authentication...")
        try:
            result = await service.upload_image_to_storage(
                user_id=user_id,
                image_content=test_content,
                original_filename=filename,
                content_type=content_type,
                user_jwt_token=None  # No authentication
            )
            
            if result:
                print(f"✅ Upload successful (no auth): {result}")
            else:
                print("❌ Upload failed (no auth)")
                
        except Exception as e:
            print(f"❌ Upload exception (no auth): {str(e)}")
        
        # Step 5: Test with fake JWT token
        print("\n📋 Step 5: Testing upload WITH fake JWT token...")
        fake_jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
        
        try:
            result = await service.upload_image_to_storage(
                user_id=user_id,
                image_content=test_content,
                original_filename=filename,
                content_type=content_type,
                user_jwt_token=fake_jwt
            )
            
            if result:
                print(f"✅ Upload successful (fake auth): {result}")
            else:
                print("❌ Upload failed (fake auth)")
                
        except Exception as e:
            print(f"❌ Upload exception (fake auth): {str(e)}")
        
        # Step 6: Test database save with file_url
        print("\n📋 Step 6: Testing database save with file_url...")
        
        analysis_data = {
            "original_filename": filename,
            "file_size": len(test_content),
            "file_type": content_type,
            "file_url": "test-debug-user-123/1234567890_abcd1234_debug-test.png",  # Fake file URL
            "score": 75,
            "complexity": {"visual": 0.7, "cognitive": 0.8},
            "areas": ["header", "content"],
            "recommendations": ["Simplify layout"],
            "analysis_summary": "Test analysis",
            "analysis_duration_ms": 1500
        }
        
        try:
            saved_analysis = await service.save_design_analysis(
                user_id=user_id,
                analysis_data=analysis_data
            )
            
            if saved_analysis:
                print(f"✅ Database save successful:")
                print(f"   - ID: {saved_analysis.get('id')}")
                print(f"   - file_url: {saved_analysis.get('file_url')}")
                print(f"   - filename: {saved_analysis.get('original_filename')}")
            else:
                print("❌ Database save failed")
                
        except Exception as e:
            print(f"❌ Database save exception: {str(e)}")
        
        print("\n🎯 DEBUG SUMMARY:")
        print("- Check the logs above to see where the process fails")
        print("- If upload fails, it's likely an authentication/RLS issue")
        print("- If database save fails, it's likely a schema/table issue")
        print("- If both work but file_url is still null in production, check the integration")
        
    except Exception as e:
        print(f"❌ Debug script failed: {str(e)}")
        logger.exception("Debug script exception")

if __name__ == "__main__":
    asyncio.run(debug_image_upload())
