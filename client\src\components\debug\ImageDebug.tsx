import React, { useState, useEffect } from 'react';

interface ImageTestResult {
  path: string;
  name: string;
  loaded: boolean;
  error?: string;
}

export function ImageDebug() {
  const [results, setResults] = useState<ImageTestResult[]>([]);

  const imagesToTest = [
    { path: "/Agent%20Marketplace.png", name: "Agent Marketplace (URL encoded)" },
    { path: "/Agent Marketplace.png", name: "Agent Marketplace (with spaces)" },
    { path: "/Editor.png", name: "Editor" },
    { path: "/Visual%20Studio.png", name: "Visual Studio (URL encoded)" },
    { path: "/Visual Studio.png", name: "Visual Studio (with spaces)" },
    { path: "/Herramientas.png", name: "Herramientas" },
    { path: "/Ads%20Central.png", name: "Ads Central (URL encoded)" },
    { path: "/Ads Central.png", name: "Ads Central (with spaces)" },
    { path: "/Emma.png", name: "<PERSON>" }
  ];

  useEffect(() => {
    const testImages = async () => {
      const testResults: ImageTestResult[] = [];

      for (const imageTest of imagesToTest) {
        try {
          const result = await testImageLoad(imageTest.path);
          testResults.push({
            path: imageTest.path,
            name: imageTest.name,
            loaded: result.loaded,
            error: result.error
          });
        } catch (error) {
          testResults.push({
            path: imageTest.path,
            name: imageTest.name,
            loaded: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      setResults(testResults);
    };

    testImages();
  }, []);

  const testImageLoad = (src: string): Promise<{ loaded: boolean; error?: string }> => {
    return new Promise((resolve) => {
      const img = new Image();
      
      img.onload = () => {
        console.log(`✅ Image loaded successfully: ${src}`);
        resolve({ loaded: true });
      };
      
      img.onerror = (error) => {
        console.error(`❌ Image failed to load: ${src}`, error);
        resolve({ loaded: false, error: 'Failed to load' });
      };
      
      img.src = src;
    });
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto my-8">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">
        🧪 Emma Studio Images Debug Test
      </h2>
      
      <div className="space-y-4">
        {results.map((result, index) => (
          <div 
            key={index}
            className={`p-4 rounded-lg border-2 ${
              result.loaded 
                ? 'border-green-200 bg-green-50' 
                : 'border-red-200 bg-red-50'
            }`}
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-gray-800">
                {result.name}
              </h3>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                result.loaded 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {result.loaded ? '✅ Loaded' : '❌ Failed'}
              </span>
            </div>
            
            <div className="text-sm text-gray-600 mb-2">
              <strong>Path:</strong> <code className="bg-gray-100 px-2 py-1 rounded">{result.path}</code>
            </div>
            
            {result.error && (
              <div className="text-sm text-red-600">
                <strong>Error:</strong> {result.error}
              </div>
            )}
            
            {result.loaded && (
              <div className="mt-3">
                <img 
                  src={result.path} 
                  alt={result.name}
                  className="max-w-xs max-h-32 object-cover rounded border"
                  style={{ maxWidth: '200px', maxHeight: '120px' }}
                />
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">
          📋 Debug Information
        </h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Total images tested: {results.length}</li>
          <li>• Successfully loaded: {results.filter(r => r.loaded).length}</li>
          <li>• Failed to load: {results.filter(r => !r.loaded).length}</li>
          <li>• Current URL: {window.location.href}</li>
          <li>• Base URL: {window.location.origin}</li>
        </ul>
      </div>
    </div>
  );
}
