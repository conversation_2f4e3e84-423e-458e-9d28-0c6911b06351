<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Emma Studio Images</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .image-test {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .image-test h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .image-test img {
            max-width: 300px;
            max-height: 200px;
            object-fit: cover;
            border: 2px solid #ddd;
            border-radius: 4px;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Emma Studio Images Test</h1>
    <p>Testing if the images from the platform features section load correctly:</p>

    <div class="image-test">
        <h3>1. Agent Marketplace.png</h3>
        <img src="/Agent Marketplace.png" alt="Agent Marketplace" 
             onload="this.nextElementSibling.textContent = '✅ Loaded successfully'" 
             onerror="this.nextElementSibling.textContent = '❌ Failed to load'">
        <div class="status">Loading...</div>
    </div>

    <div class="image-test">
        <h3>2. Editor.png</h3>
        <img src="/Editor.png" alt="Editor" 
             onload="this.nextElementSibling.textContent = '✅ Loaded successfully'" 
             onerror="this.nextElementSibling.textContent = '❌ Failed to load'">
        <div class="status">Loading...</div>
    </div>

    <div class="image-test">
        <h3>3. Visual Studio.png</h3>
        <img src="/Visual Studio.png" alt="Visual Studio" 
             onload="this.nextElementSibling.textContent = '✅ Loaded successfully'" 
             onerror="this.nextElementSibling.textContent = '❌ Failed to load'">
        <div class="status">Loading...</div>
    </div>

    <div class="image-test">
        <h3>4. Herramientas.png</h3>
        <img src="/Herramientas.png" alt="Herramientas" 
             onload="this.nextElementSibling.textContent = '✅ Loaded successfully'" 
             onerror="this.nextElementSibling.textContent = '❌ Failed to load'">
        <div class="status">Loading...</div>
    </div>

    <div class="image-test">
        <h3>5. Ads Central.png</h3>
        <img src="/Ads Central.png" alt="Ads Central" 
             onload="this.nextElementSibling.textContent = '✅ Loaded successfully'" 
             onerror="this.nextElementSibling.textContent = '❌ Failed to load'">
        <div class="status">Loading...</div>
    </div>

    <div class="image-test">
        <h3>URL Encoded versions (for spaces):</h3>
        <h4>Agent Marketplace (URL encoded):</h4>
        <img src="/Agent%20Marketplace.png" alt="Agent Marketplace Encoded" 
             onload="this.nextElementSibling.textContent = '✅ URL encoded version loaded'" 
             onerror="this.nextElementSibling.textContent = '❌ URL encoded version failed'">
        <div class="status">Loading...</div>

        <h4>Visual Studio (URL encoded):</h4>
        <img src="/Visual%20Studio.png" alt="Visual Studio Encoded" 
             onload="this.nextElementSibling.textContent = '✅ URL encoded version loaded'" 
             onerror="this.nextElementSibling.textContent = '❌ URL encoded version failed'">
        <div class="status">Loading...</div>

        <h4>Ads Central (URL encoded):</h4>
        <img src="/Ads%20Central.png" alt="Ads Central Encoded" 
             onload="this.nextElementSibling.textContent = '✅ URL encoded version loaded'" 
             onerror="this.nextElementSibling.textContent = '❌ URL encoded version failed'">
        <div class="status">Loading...</div>
    </div>

    <script>
        console.log('🧪 Image test page loaded');
        
        // Log all image load attempts
        document.querySelectorAll('img').forEach((img, index) => {
            img.addEventListener('load', () => {
                console.log(`✅ Image ${index + 1} loaded successfully:`, img.src);
            });
            
            img.addEventListener('error', () => {
                console.error(`❌ Image ${index + 1} failed to load:`, img.src);
            });
        });
    </script>
</body>
</html>
