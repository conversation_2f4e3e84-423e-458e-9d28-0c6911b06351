import React from "react";
import { motion } from "framer-motion";
import usePostGenerationProgress from "../hooks/usePostGenerationProgress";

export interface EnhancedLoadingScreenProps {
  brandName?: string;
  estimatedTime?: number;
  isGenerating: boolean;
}

const EnhancedLoadingScreen: React.FC<EnhancedLoadingScreenProps> = ({
  brandName = "tu marca",
  estimatedTime = 45, // 45 seconds optimized
  isGenerating
}) => {
  const progressData = usePostGenerationProgress(isGenerating, estimatedTime);

  // Dynamic motivational messages based on current stage
  const getMotivationalMessage = () => {
    const messages = [
      "Analizando cada detalle de tu marca para crear contenido único...",
      "Generando textos que conectarán con tu audiencia...",
      "Creando imágenes impactantes que destacarán en redes sociales...",
      "Puliendo cada detalle para garantizar la máxima calidad..."
    ];
    return messages[progressData.currentStage] || messages[0];
  };

  // Tips that appear during different stages
  const getCurrentTip = () => {
    const tips = [
      "💡 Tip: Un buen análisis de marca es la base del contenido exitoso",
      "✍️ Tip: El contenido personalizado genera 6x más engagement",
      "🎨 Tip: Las imágenes profesionales aumentan el alcance hasta 94%",
      "🚀 Tip: La consistencia visual fortalece el reconocimiento de marca"
    ];
    return tips[progressData.currentStage] || tips[0];
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#3018ef] via-[#4c51bf] to-[#dd3a5a] flex items-center justify-center relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-32 h-32 bg-white rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-24 h-24 bg-white rounded-full blur-2xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white rounded-full blur-xl animate-pulse delay-500"></div>
        <div className="absolute top-1/3 right-1/3 w-20 h-20 bg-white rounded-full blur-2xl animate-pulse delay-1500"></div>
      </div>

      <div className="relative z-10 text-center max-w-2xl mx-auto px-6">
        {/* Animated Pencil */}
        <div className="mb-8 flex justify-center">
          <motion.div
            className="relative"
            animate={{
              rotate: [0, 5, -5, 0],
              y: [0, -10, 0]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            {/* Pencil SVG */}
            <svg
              width="100"
              height="100"
              viewBox="0 0 100 100"
              className="drop-shadow-lg"
            >
              {/* Pencil body */}
              <motion.rect
                x="35"
                y="20"
                width="30"
                height="60"
                rx="15"
                fill="#FFD700"
                initial={{ scaleY: 0 }}
                animate={{ scaleY: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              />
              
              {/* Pencil tip */}
              <motion.polygon
                points="50,80 40,90 60,90"
                fill="#8B4513"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.7 }}
              />
              
              {/* Pencil eraser */}
              <motion.circle
                cx="50"
                cy="20"
                r="8"
                fill="#FF69B4"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.3, delay: 0.5 }}
              />
              
              {/* Metal band */}
              <motion.rect
                x="42"
                y="15"
                width="16"
                height="10"
                fill="#C0C0C0"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.2, delay: 0.6 }}
              />
              
              {/* Writing lines animation */}
              <motion.path
                d="M 70 85 Q 80 80 85 75"
                stroke="white"
                strokeWidth="2"
                fill="none"
                strokeLinecap="round"
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{ pathLength: 1, opacity: [0, 1, 0] }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: 1,
                  ease: "easeInOut"
                }}
              />
            </svg>
          </motion.div>
        </div>

        {/* Main heading */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="mb-6"
        >
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-4 drop-shadow-lg">
            El Marketing Ya Cambió
          </h1>
          <div className="w-32 h-1 bg-gradient-to-r from-white/50 to-transparent mx-auto rounded-full"></div>
        </motion.div>

        {/* Subtitle with brand name */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.5 }}
          className="mb-8"
        >
          <p className="text-xl md:text-2xl text-white/90 font-light drop-shadow mb-4">
            Creando contenido increíble para{" "}
            <span className="font-semibold text-yellow-300">{brandName}</span>
          </p>

          {/* Dynamic motivational message */}
          <motion.p
            key={progressData.currentStage}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-white/70 text-sm italic"
          >
            {getMotivationalMessage()}
          </motion.p>
        </motion.div>

        {/* Progress section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 2 }}
          className="mb-8"
        >
          {/* Progress bar */}
          <div className="w-full max-w-md mx-auto mb-6">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-white/80">Progreso</span>
              <span className="text-sm font-medium text-white/80">{Math.round(progressData.progress)}%</span>
            </div>
            <div className="w-full bg-white/20 rounded-full h-3 backdrop-blur-sm">
              <motion.div
                className="bg-gradient-to-r from-yellow-400 to-yellow-300 h-3 rounded-full shadow-lg"
                initial={{ width: 0 }}
                animate={{ width: `${progressData.progress}%` }}
                transition={{ duration: 0.5, ease: "easeOut" }}
              />
            </div>
          </div>

          {/* Current stage */}
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
            <motion.div
              key={progressData.currentStage}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex items-center justify-center mb-3">
                <motion.div
                  className="w-3 h-3 bg-yellow-400 rounded-full mr-3"
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1, repeat: Infinity }}
                />
                <h3 className="text-lg font-semibold text-white">
                  {progressData.stages[progressData.currentStage]?.name}
                </h3>
              </div>
              <p className="text-white/80 text-sm">
                {progressData.stages[progressData.currentStage]?.description}
              </p>
            </motion.div>
          </div>
        </motion.div>

        {/* Time and stages indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 2.5 }}
          className="space-y-4"
        >
          {/* Time remaining */}
          <div className="text-center">
            <p className="text-white/70 text-sm mb-1">Tiempo restante estimado</p>
            <p className="text-white font-mono text-lg">{progressData.formattedRemainingTime}</p>
          </div>

          {/* Stages indicator */}
          <div className="flex justify-center space-x-4">
            {progressData.stages.map((stage, index) => (
              <motion.div
                key={index}
                className={`flex flex-col items-center space-y-2 ${
                  index <= progressData.currentStage ? 'opacity-100' : 'opacity-50'
                }`}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: index <= progressData.currentStage ? 1 : 0.5, y: 0 }}
                transition={{ delay: 2.5 + index * 0.1 }}
              >
                <motion.div
                  className={`w-8 h-8 rounded-full border-2 flex items-center justify-center ${
                    stage.completed
                      ? 'bg-yellow-400 border-yellow-400 text-[#3018ef]'
                      : index === progressData.currentStage
                      ? 'bg-white border-white text-[#3018ef]'
                      : 'bg-transparent border-white/50 text-white/50'
                  }`}
                  animate={index === progressData.currentStage ? { scale: [1, 1.1, 1] } : {}}
                  transition={{ duration: 1, repeat: Infinity }}
                >
                  {stage.completed ? (
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <span className="text-xs font-bold">{index + 1}</span>
                  )}
                </motion.div>
                <span className="text-xs text-white/70 text-center max-w-16">
                  {stage.name.split(' ')[0]}
                </span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Quality message */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 3 }}
          className="mt-8 text-center"
        >
          <p className="text-white/60 text-sm mb-3">
            ⚡ Generando contenido de máxima calidad con IA avanzada
          </p>

          {/* Dynamic tip */}
          <motion.div
            key={progressData.currentStage}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-white/5 backdrop-blur-sm rounded-lg p-3 mb-3 border border-white/10"
          >
            <p className="text-white/80 text-xs">
              {getCurrentTip()}
            </p>
          </motion.div>

          {progressData.progress > 80 && (
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-yellow-300 text-sm mt-2 font-medium"
            >
              🎉 ¡Casi terminado! Preparando tus posts...
            </motion.p>
          )}

          {/* Elapsed time display */}
          <div className="mt-4 text-white/50 text-xs">
            Tiempo transcurrido: {progressData.formattedElapsedTime}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default EnhancedLoadingScreen;
