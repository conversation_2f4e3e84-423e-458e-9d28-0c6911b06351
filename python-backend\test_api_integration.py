#!/usr/bin/env python
"""
Test script for the API integration.
This script tests the API endpoints for the agent system.
"""

import os
import sys
import json
import asyncio
import logging
import uuid
import requests
from typing import Dict, Any, Optional, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# API URL
API_URL = "http://localhost:8000"

def test_crew_run_endpoint():
    """Test the /api/v1/crew/run endpoint."""
    # Create a request
    request_data = {
        "crew_id": "test_crew",
        "prompt": "Create a blog post about AI in marketing",
        "input_params": {
            "format": "markdown",
            "length": "medium"
        }
    }
    
    # Send the request
    try:
        logger.info("Testing /api/v1/crew/run endpoint...")
        response = requests.post(
            f"{API_URL}/api/v1/crew/run",
            json=request_data
        )
        
        # Check the response
        if response.status_code == 200:
            logger.info("Request successful!")
            response_data = response.json()
            logger.info(f"Response: {json.dumps(response_data, indent=2)}")
            return True
        else:
            logger.error(f"Request failed with status code {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
    except Exception as e:
        logger.error(f"Error testing /api/v1/crew/run endpoint: {e}")
        return False

def test_agent_chat_endpoint():
    """Test the /api/v1/agent/chat endpoint."""
    # Create a request
    request_data = {
        "agent_id": "emma",
        "message": "Tell me about content marketing",
        "session_id": str(uuid.uuid4())
    }
    
    # Send the request
    try:
        logger.info("Testing /api/v1/agent/chat endpoint...")
        response = requests.post(
            f"{API_URL}/api/v1/agent/chat",
            json=request_data
        )
        
        # Check the response
        if response.status_code == 200:
            logger.info("Request successful!")
            response_data = response.json()
            logger.info(f"Response: {json.dumps(response_data, indent=2)}")
            return True
        else:
            logger.error(f"Request failed with status code {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
    except Exception as e:
        logger.error(f"Error testing /api/v1/agent/chat endpoint: {e}")
        return False

def run_tests():
    """Run all tests."""
    # Check if the API is running
    try:
        logger.info("Checking if the API is running...")
        response = requests.get(f"{API_URL}/docs")
        if response.status_code != 200:
            logger.error("API is not running. Please start the API first.")
            return False
    except Exception as e:
        logger.error(f"Error checking if the API is running: {e}")
        logger.error("Please start the API with 'python -m backend.main' first.")
        return False
    
    # Test the endpoints
    crew_run_success = test_crew_run_endpoint()
    agent_chat_success = test_agent_chat_endpoint()
    
    # Check if all tests passed
    if crew_run_success and agent_chat_success:
        logger.info("All API integration tests passed!")
        return True
    else:
        logger.error("Some API integration tests failed!")
        return False

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
