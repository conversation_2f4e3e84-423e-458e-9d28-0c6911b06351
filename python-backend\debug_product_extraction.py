#!/usr/bin/env python3
"""
Debug script for product extraction issues
Analyzes why the system returns 0 products for known brands
"""

import asyncio
import aiohttp
import json
from bs4 import BeautifulSoup
import re
from urllib.parse import urlparse

async def debug_nike_extraction():
    """Debug Nike product extraction step by step"""
    print("🔍 DEBUGGING NIKE PRODUCT EXTRACTION")
    print("=" * 60)
    
    nike_urls = [
        "https://www.nike.com/",
        "https://www.nike.com/w/mens-shoes-nik1zy7ok",
        "https://www.nike.com/t/air-max-270-mens-shoes-KkLcGR"
    ]
    
    async with aiohttp.ClientSession() as session:
        for url in nike_urls:
            print(f"\n🎯 Testing URL: {url}")
            print("-" * 40)
            
            try:
                async with session.get(url, timeout=15) as response:
                    print(f"   Status Code: {response.status}")
                    print(f"   Content-Type: {response.headers.get('content-type', 'unknown')}")
                    
                    if response.status == 200:
                        html_content = await response.text()
                        soup = BeautifulSoup(html_content, 'html.parser')
                        
                        # Check for JSON-LD
                        json_ld_scripts = soup.find_all('script', type='application/ld+json')
                        print(f"   JSON-LD scripts found: {len(json_ld_scripts)}")
                        
                        for i, script in enumerate(json_ld_scripts[:3]):  # Check first 3
                            try:
                                data = json.loads(script.string)
                                print(f"     Script {i+1}: {type(data)} - {list(data.keys()) if isinstance(data, dict) else 'Not dict'}")
                                
                                if isinstance(data, dict) and '@type' in data:
                                    print(f"       @type: {data.get('@type')}")
                                elif isinstance(data, list):
                                    for j, item in enumerate(data[:2]):
                                        if isinstance(item, dict) and '@type' in item:
                                            print(f"       Item {j}: @type: {item.get('@type')}")
                            except json.JSONDecodeError as e:
                                print(f"     Script {i+1}: JSON decode error: {e}")
                        
                        # Check for microdata
                        microdata_elements = soup.find_all(attrs={"itemtype": re.compile(r".*Product.*", re.I)})
                        print(f"   Microdata product elements: {len(microdata_elements)}")
                        
                        # Check for common product selectors
                        product_selectors = [
                            ".product", ".product-item", ".product-card",
                            "[data-product]", ".item", ".listing-item"
                        ]
                        
                        for selector in product_selectors:
                            elements = soup.select(selector)
                            if elements:
                                print(f"   Found {len(elements)} elements with selector '{selector}'")
                        
                        # Check page title and meta
                        title = soup.find('title')
                        print(f"   Page title: {title.get_text(strip=True) if title else 'None'}")
                        
                        # Look for product-specific patterns
                        product_patterns = [
                            r'\$\d+\.?\d*',  # Price patterns
                            r'add.to.cart',  # Add to cart
                            r'buy.now',      # Buy now
                            r'product.id',   # Product ID
                        ]
                        
                        text_content = soup.get_text().lower()
                        for pattern in product_patterns:
                            matches = re.findall(pattern, text_content, re.IGNORECASE)
                            if matches:
                                print(f"   Found pattern '{pattern}': {len(matches)} matches")
                    
                    else:
                        print(f"   ❌ Failed to fetch: HTTP {response.status}")
                        
            except Exception as e:
                print(f"   ❌ Error: {e}")

async def debug_serper_search():
    """Debug Serper API search for Nike products"""
    print("\n🔍 DEBUGGING SERPER API SEARCH")
    print("=" * 60)
    
    serper_api_key = "2187e03c0d1710eeaa3e3669daf6a4fcddc1b84cb"
    
    search_queries = [
        "Nike products",
        "Nike shoes buy online",
        "site:nike.com products"
    ]
    
    async with aiohttp.ClientSession() as session:
        for query in search_queries:
            print(f"\n🎯 Testing query: '{query}'")
            print("-" * 40)
            
            try:
                headers = {
                    'X-API-KEY': serper_api_key,
                    'Content-Type': 'application/json'
                }
                
                payload = {
                    'q': query,
                    'gl': 'us',
                    'hl': 'en',
                    'num': 5
                }
                
                async with session.post(
                    'https://google.serper.dev/search',
                    headers=headers,
                    json=payload,
                    timeout=10
                ) as response:
                    
                    print(f"   Status Code: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        organic_results = data.get('organic', [])
                        print(f"   Organic results: {len(organic_results)}")
                        
                        for i, result in enumerate(organic_results[:3]):
                            title = result.get('title', 'No title')
                            link = result.get('link', 'No link')
                            snippet = result.get('snippet', 'No snippet')
                            
                            print(f"     Result {i+1}:")
                            print(f"       Title: {title[:60]}...")
                            print(f"       Link: {link}")
                            print(f"       Snippet: {snippet[:80]}...")
                            
                            # Check if it's a product page
                            is_product_page = any(keyword in link.lower() for keyword in [
                                'product', 'item', 'shop', 'buy', '/p/', '/products/'
                            ])
                            print(f"       Looks like product page: {is_product_page}")
                        
                        # Check related searches
                        related_searches = data.get('relatedSearches', [])
                        print(f"   Related searches: {len(related_searches)}")
                        
                    else:
                        error_text = await response.text()
                        print(f"   ❌ API Error: {response.status}")
                        print(f"   Error details: {error_text[:200]}...")
                        
            except Exception as e:
                print(f"   ❌ Error: {e}")

async def test_real_ecommerce_sites():
    """Test product extraction on known ecommerce sites"""
    print("\n🔍 TESTING REAL ECOMMERCE SITES")
    print("=" * 60)
    
    test_sites = [
        {
            "name": "Amazon Product Page",
            "url": "https://www.amazon.com/dp/B08N5WRWNW",  # Echo Dot
            "expected_patterns": ["product", "price", "add to cart"]
        },
        {
            "name": "Best Buy Product",
            "url": "https://www.bestbuy.com/site/apple-iphone-15-128gb-black-verizon/6525421.p",
            "expected_patterns": ["product", "price", "add to cart"]
        },
        {
            "name": "Target Product",
            "url": "https://www.target.com/p/apple-iphone-15-128gb/-/A-89957177",
            "expected_patterns": ["product", "price", "add to cart"]
        }
    ]
    
    async with aiohttp.ClientSession() as session:
        for site in test_sites:
            print(f"\n🎯 Testing: {site['name']}")
            print(f"   URL: {site['url']}")
            print("-" * 40)
            
            try:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
                
                async with session.get(site['url'], headers=headers, timeout=15) as response:
                    print(f"   Status Code: {response.status}")
                    
                    if response.status == 200:
                        html_content = await response.text()
                        soup = BeautifulSoup(html_content, 'html.parser')
                        
                        # Check for JSON-LD
                        json_ld_scripts = soup.find_all('script', type='application/ld+json')
                        print(f"   JSON-LD scripts: {len(json_ld_scripts)}")
                        
                        products_found = 0
                        for script in json_ld_scripts:
                            try:
                                data = json.loads(script.string)
                                if isinstance(data, dict):
                                    if 'product' in str(data.get('@type', '')).lower():
                                        products_found += 1
                                        print(f"     Found Product schema: {data.get('name', 'No name')}")
                                elif isinstance(data, list):
                                    for item in data:
                                        if isinstance(item, dict) and 'product' in str(item.get('@type', '')).lower():
                                            products_found += 1
                                            print(f"     Found Product schema: {item.get('name', 'No name')}")
                            except:
                                continue
                        
                        print(f"   Products found in JSON-LD: {products_found}")
                        
                        # Check for expected patterns
                        page_text = soup.get_text().lower()
                        for pattern in site['expected_patterns']:
                            if pattern in page_text:
                                print(f"   ✅ Found pattern: '{pattern}'")
                            else:
                                print(f"   ❌ Missing pattern: '{pattern}'")
                    
                    else:
                        print(f"   ❌ Failed to fetch: HTTP {response.status}")
                        
            except Exception as e:
                print(f"   ❌ Error: {e}")

async def debug_current_service():
    """Debug the current Real SEO service with detailed logging"""
    print("\n🔍 DEBUGGING CURRENT SERVICE")
    print("=" * 60)
    
    # Test the actual service endpoint with debug info
    async with aiohttp.ClientSession() as session:
        try:
            payload = {"brand_name": "Nike"}
            
            print("🎯 Testing current service with Nike...")
            
            async with session.post(
                "http://localhost:8000/api/real-seo-ecommerce/analyze",
                json=payload,
                timeout=30
            ) as response:
                
                print(f"   Status Code: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    
                    print(f"   Total Products: {data.get('total_products', 0)}")
                    print(f"   Keywords: {len(data.get('seo_keywords', []))}")
                    print(f"   Data Sources: {data.get('data_sources', [])}")
                    print(f"   Status: {data.get('status')}")
                    print(f"   Message: {data.get('message', 'No message')}")
                    
                    # Check if any products were found
                    products = data.get('real_products', [])
                    if products:
                        print(f"   Sample product: {products[0].get('title', 'No title')}")
                    else:
                        print("   ❌ No products found - this is the issue!")
                        
                else:
                    error_text = await response.text()
                    print(f"   ❌ Service Error: {response.status}")
                    print(f"   Error: {error_text[:200]}...")
                    
        except Exception as e:
            print(f"   ❌ Error: {e}")

async def main():
    """Run all debug tests"""
    print("🐛 PRODUCT EXTRACTION DEBUG ANALYSIS")
    print("=" * 80)
    print("Investigating why the system returns 0 products for known brands")
    print("=" * 80)
    
    await debug_nike_extraction()
    await debug_serper_search()
    await test_real_ecommerce_sites()
    await debug_current_service()
    
    print("\n📋 DEBUG SUMMARY")
    print("=" * 60)
    print("This analysis will help identify:")
    print("1. Whether websites are accessible and contain product data")
    print("2. If Serper API is finding the right product pages")
    print("3. Whether JSON-LD/microdata extraction is working")
    print("4. Where the current service is failing")

if __name__ == "__main__":
    asyncio.run(main())
