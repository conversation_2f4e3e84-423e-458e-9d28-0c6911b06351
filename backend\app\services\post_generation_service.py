"""
Main post generation service that orchestrates all components.
"""

import logging
import uuid
from typing import Dict, Any, List

from app.models.post_models import GeneratedPost, PostGenerationResponse
from app.services.ai_content_intelligence import AIContentIntelligence
from app.services.content_generation_service import ContentGenerationService
from app.services.image_generation_service import ImageGenerationService
from app.services.ideogram_service import IdeogramService
from app.services.watermark_service import watermark_service
from app.services.creative_genius_service import CreativeGeniusService
from app.services.text_overlay_service import text_overlay_service
from app.utils.platform_utils import get_platform_dimensions

logger = logging.getLogger(__name__)


class PostGenerationService:
    """Main service for orchestrating post generation with all components."""
    
    def __init__(self):
        self.ai_intelligence = AIContentIntelligence()
        self.content_service = ContentGenerationService()
        self.image_service = ImageGenerationService()
        self.ideogram_service = IdeogramService()
        self.creative_genius = CreativeGeniusService()  # 🧠 EL CEREBRO CREATIVO

        logger.info("✅ Post Generation Service initialized with Creative Genius")
    
    async def generate_posts_batch(self, brand_info: Dict[str, Any], design_config: Dict[str, Any], generation_config: Dict[str, Any]) -> PostGenerationResponse:
        """
        Generate multiple posts using the intelligent content system.
        
        Args:
            brand_info: Brand information from steps 1-3
            design_config: Design configuration from step 4
            generation_config: Generation settings
            
        Returns:
            PostGenerationResponse with generated posts
        """
        try:
            # Extract configuration
            template = design_config.get("selectedTheme", "Balance")
            platform = design_config.get("platform", "Instagram")
            post_count = generation_config.get("count", 3)
            
            logger.info(f"🚀 Starting batch generation: {post_count} posts, template: {template}, platform: {platform}")
            
            # Get user topic from brand info
            user_topic = None
            if brand_info.get("topics") and len(brand_info.get("topics", [])) > 0:
                user_topic = brand_info.get("topics")[0]
            
            # Generate intelligent content plan with context analysis
            content_strategy = await self.ai_intelligence.generate_strategic_content_plan(template, brand_info, platform, user_topic)
            logger.info(f"📋 Content strategy for topic '{content_strategy.get('topic', 'unknown')}': {content_strategy.get('context_analysis', {}).get('nicho', 'general')}")
            
            # Get platform-specific dimensions
            context_analysis = content_strategy.get("context_analysis", {})
            content_type = context_analysis.get("tipo_contenido", "educational")
            image_dimensions = get_platform_dimensions(platform, content_type)
            logger.info(f"🖼️ Using platform-specific dimensions: {image_dimensions} for {platform}")
            
            # 🚀 SPEED OPTIMIZATION: Generate ONE concept and reuse for batch (3x faster)
            logger.info(f"🎨 Creative Genius creating ONE breakthrough concept for batch generation...")

            try:
                # Use Creative Genius to create revolutionary concept
                breakthrough = await self.creative_genius.create_breakthrough_content(
                    user_context=brand_info,
                    content_type=content_type,
                    user_topic=user_topic  # Pass the actual user topic/instructions
                )

                # Reuse the same concept for all posts (they'll have different content but same visual theme)
                creative_concepts = [breakthrough] * post_count

                logger.info(f"🚀 Breakthrough concept created! Viral score: {breakthrough.viral_score}")
                logger.info(f"💡 Why it's brilliant: {breakthrough.why_its_brilliant}")
                logger.info(f"🔄 Reusing concept for {post_count} posts (3x speed boost)")

            except Exception as e:
                logger.error(f"❌ Creative Genius failed: {e}")
                # Fallback to traditional method
                visual_hook = await self.content_service.generate_visual_hook_content(content_strategy, brand_info)
                from app.services.creative_genius_service import CreativeBreakthrough
                fallback_concept = CreativeBreakthrough(
                    visual_concept="Professional social media design",
                    hook=visual_hook,
                    content_angle="traditional_approach",
                    ideogram_prompt=f'A professional social media graphic with text that reads: "{visual_hook}"',
                    emotional_journey={"hook_emotion": "interest", "middle_emotion": "engagement", "end_emotion": "action"},
                    viral_score=6.0,
                    why_its_brilliant="Solid traditional approach with clear messaging",
                    psychology_target="general_interest",
                    art_direction="professional"
                )
                creative_concepts = [fallback_concept] * post_count

            logger.info(f"🧠 Creative Genius optimized: 1 concept reused for {len(creative_concepts)} posts")
            
            # 🚀 BATCH GENERATION: Generate ALL images at once for speed
            logger.info(f"🚀 BATCH GENERATING {post_count} images simultaneously...")

            # Use the first concept for batch generation (they're all similar anyway)
            main_concept = creative_concepts[0]
            clean_visual_prompt = self._create_clean_background_prompt(main_concept)

            logger.info(f"🎨 Batch generating CLEAN background images (Two-Layer System)...")
            logger.info(f"🧠 Creative concept: {main_concept.visual_concept[:100]}...")
            logger.info(f"🖼️ Clean background prompt: {clean_visual_prompt[:200]}...")

            # 🚀 GENERATE ALL IMAGES AT ONCE
            batch_result = await self.ideogram_service.generate_batch_images(
                prompt=clean_visual_prompt,
                num_images=post_count,
                size=f"{image_dimensions['width']}x{image_dimensions['height']}"
            )

            image_urls = []
            image_metadata_list = []

            if batch_result.get("success") and batch_result.get("image_urls"):
                image_urls = batch_result["image_urls"]
                image_metadata_list = batch_result.get("metadata_list", [])
                logger.info(f"✅ BATCH GENERATED {len(image_urls)} images successfully!")

                # Store images locally for future reference
                for i, image_url in enumerate(image_urls):
                    try:
                        from app.services.image_storage_service import image_storage_service
                        stored_path = await image_storage_service.store_image(image_url)
                        if stored_path:
                            logger.info(f"📁 Stored batch image {i+1} locally")
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to store batch image {i+1}: {e}")
            else:
                logger.error(f"❌ Batch image generation failed: {batch_result.get('error')}")
                # Fallback to individual generation if batch fails
                for i, concept in enumerate(creative_concepts):
                    try:
                        clean_visual_prompt = self._create_clean_background_prompt(concept)
                        image_result = await self.ideogram_service.generate_image(
                            prompt=clean_visual_prompt,
                            dimensions=image_dimensions
                        )

                        if image_result and image_result.get("success") and image_result.get("image_url"):
                            image_url = image_result["image_url"]

                            # Store image locally
                            from app.services.image_storage_service import image_storage_service
                            stored_path = await image_storage_service.store_image(image_url)
                            if stored_path:
                                logger.info(f"📁 Stored fallback image {i+1} locally")

                            image_urls.append(image_url)
                            image_metadata_list.append(image_result.get("metadata", {}))
                        else:
                            image_urls.append(None)
                            image_metadata_list.append({})

                    except Exception as e:
                        logger.error(f"❌ Error generating fallback image {i+1}: {e}")
                        image_urls.append(None)
                        image_metadata_list.append({})

            # 🚀 BATCH WATERMARK APPLICATION (if enabled)
            if watermark_service.is_enabled() and image_urls:
                logger.info("🚀 Applying watermarks to batch images...")
                for i, image_url in enumerate(image_urls):
                    if image_url:
                        try:
                            watermarked_url = await watermark_service.add_watermark(image_url)
                            if watermarked_url:
                                image_urls[i] = watermarked_url
                                logger.info(f"✅ Applied watermark to batch image {i+1}")
                        except Exception as e:
                            logger.warning(f"⚠️ Watermark failed for batch image {i+1}: {e}")
            
            # 🚀 PARALLEL POST GENERATION: Create all posts simultaneously
            logger.info(f"🚀 Creating {post_count} posts in parallel...")

            async def create_single_post(i: int) -> GeneratedPost:
                """Create a single post with text overlay."""
                try:
                    concept = creative_concepts[i] if i < len(creative_concepts) else creative_concepts[0]

                    # Generate content based on Creative Genius concept
                    post_content = await self.content_service.generate_creative_genius_content(
                        creative_concept=concept,
                        brand_info=brand_info,
                        platform=platform
                    )

                    # Create post object
                    post = GeneratedPost(
                        id=str(uuid.uuid4()),
                        text=post_content,
                        image_url=image_urls[i] if i < len(image_urls) else None,
                        template=template,
                        platform=platform,
                        metadata={
                            "visual_hook": concept.hook,
                            "creative_concept": concept.visual_concept,
                            "content_angle": concept.content_angle,
                            "viral_score": concept.viral_score,
                            "psychology_target": concept.psychology_target,
                            "art_direction": concept.art_direction,
                            "why_brilliant": concept.why_its_brilliant,
                            "brand_name": brand_info.get("businessName", "Unknown"),
                            "generation_index": i+1,
                            "provider": "ideogram",
                            "image_generated": image_urls[i] is not None if i < len(image_urls) else False,
                            "dimensions": image_dimensions,
                            "content_type": content_strategy.get("context_analysis", {}).get("tipo_contenido", "educational"),
                            "batch_generated": True,
                            "generation_method": "two_layer_system",
                            "creative_engine": "breakthrough_content_generation",
                            "text_overlay": {
                                "text": concept.hook,
                                "font_family": "Inter, sans-serif",
                                "font_size": "auto",
                                "font_weight": "bold",
                                "color": "#FFFFFF",
                                "position": "center",
                                "background_opacity": 0.7,
                                "text_shadow": True
                            },
                            "background_image": {
                                "type": "clean_background",
                                "has_embedded_text": False,
                                "optimized_for_overlay": True,
                                "clean_prompt_used": True
                            },
                            "seed": image_metadata_list[i].get("seed") if i < len(image_metadata_list) else None,
                            "style_type": image_metadata_list[i].get("style_type", "DESIGN") if i < len(image_metadata_list) else "DESIGN"
                        }
                    )

                    # 🚀 PARALLEL TEXT OVERLAY COMPOSITION
                    if post.image_url and post.metadata.get("text_overlay"):
                        try:
                            composed_result = await text_overlay_service.compose_text_overlay(
                                background_image_url=post.image_url,
                                text=post.metadata["text_overlay"]["text"],
                                overlay_config=post.metadata["text_overlay"],
                                dimensions=image_dimensions
                            )

                            if composed_result.get("success"):
                                post.image_url = composed_result["image_data"]
                                post.metadata["composed_image"] = True
                                post.metadata["composition_successful"] = True
                            else:
                                post.metadata["composition_successful"] = False

                        except Exception as e:
                            logger.error(f"❌ Error composing text overlay for post {i+1}: {e}")
                            post.metadata["composition_successful"] = False

                    return post

                except Exception as e:
                    logger.error(f"❌ Error creating post {i+1}: {e}")
                    return None

            # 🚀 CREATE ALL POSTS IN PARALLEL
            import asyncio
            post_tasks = [create_single_post(i) for i in range(post_count)]
            post_results = await asyncio.gather(*post_tasks, return_exceptions=True)

            # Filter successful posts
            generated_posts = [post for post in post_results if post is not None and not isinstance(post, Exception)]
            logger.info(f"✅ Generated {len(generated_posts)}/{post_count} posts in parallel")
            
            # Create response
            response = PostGenerationResponse(
                success=len(generated_posts) > 0,
                posts=generated_posts,
                total_generated=len(generated_posts),
                error=None if len(generated_posts) > 0 else "Failed to generate any posts",
                metadata={
                    "template": template,
                    "platform": platform,
                    "content_type": content_strategy.get("context_analysis", {}).get("tipo_contenido", "educational"),
                    "content_strategy": content_strategy.get("context_analysis", {}).get("tipo_contenido", "educational"),
                    "images_generated": sum(1 for post in generated_posts if post.image_url is not None),
                    "provider": "ideogram",
                    "dimensions": image_dimensions,
                    "platform_optimized": True,
                    "batch_generated": True,
                    "generation_method": "strategic_separation",
                    "content_quality": "professional_ai_generated",
                    "visual_textual_separation": True
                }
            )
            
            logger.info(f"🎉 Batch generation completed: {len(generated_posts)}/{post_count} posts generated successfully")
            return response
            
        except Exception as e:
            logger.error(f"❌ Critical error in batch generation: {e}")
            return PostGenerationResponse(
                success=False,
                posts=[],
                total_generated=0,
                error=f"Failed to generate posts: {str(e)}",
                metadata={}
            )
    
    async def generate_single_post(self, brand_info: Dict[str, Any], design_config: Dict[str, Any]) -> PostGenerationResponse:
        """
        Generate a single post (legacy endpoint support).

        Args:
            brand_info: Brand information
            design_config: Design configuration

        Returns:
            PostGenerationResponse with single generated post
        """
        generation_config = {"count": 1}
        return await self.generate_posts_batch(brand_info, design_config, generation_config)

    async def generate_similar_posts(self, brand_info: Dict[str, Any], reference_post: Dict[str, Any], generation_config: Dict[str, Any]) -> PostGenerationResponse:
        """
        Generate posts similar to a reference post using the dedicated SimilarPostsService.

        Args:
            brand_info: Brand information
            reference_post: The post to use as reference
            generation_config: Generation settings

        Returns:
            PostGenerationResponse with similar generated posts
        """
        try:
            from app.services.similar_posts_service import similar_posts_service

            count = generation_config.get("count", 3)
            logger.info(f"🎯 Starting similar post generation: {count} posts based on reference")

            # Extract reference post details
            reference_content = reference_post.get("content", "")
            reference_template = reference_post.get("template", "Balance")
            reference_platform = reference_post.get("platform", "Instagram")

            logger.info(f"📋 Reference post template: {reference_template}, platform: {reference_platform}")
            logger.info(f"📝 Reference content: {reference_content[:100]}...")

            # Create user context for SimilarPostsService
            user_context = {
                "businessName": brand_info.get("businessName", "Business"),
                "industry": brand_info.get("industry", "general"),
                "target_audience": brand_info.get("target_audience", "general audience"),
                "brandColor": brand_info.get("brandColor") or brand_info.get("brand_color"),
                "voice": brand_info.get("voice", "professional")
            }

            # Use the dedicated SimilarPostsService
            logger.info(f"🔄 Using dedicated SimilarPostsService for similarity")
            similar_posts_data = await similar_posts_service.generate_similar_posts(
                reference_post, user_context, count
            )

            if not similar_posts_data:
                logger.error("❌ SimilarPostsService returned no similar posts")
                return PostGenerationResponse(
                    success=False,
                    error="Failed to generate similar posts",
                    posts=[],
                    total_generated=0
                )

            # Convert to GeneratedPost objects
            posts = []
            for i, post_data in enumerate(similar_posts_data):
                try:
                    # Get image URL from SimilarPostsService (already generated)
                    image_url = post_data.get("image_url")

                    # Apply watermark if enabled and image exists
                    if image_url and watermark_service.is_enabled():
                        try:
                            watermarked_url = await watermark_service.add_watermark(image_url)
                            if watermarked_url:
                                image_url = watermarked_url
                        except Exception as e:
                            logger.warning(f"⚠️ Watermark failed for similar image {i+1}: {e}")

                    # Create post object
                    post = GeneratedPost(
                        id=str(uuid.uuid4()),
                        text=post_data.get("content", ""),
                        image_url=image_url,
                        template=reference_template,
                        platform=reference_platform,
                        metadata={
                            "visual_hook": post_data.get("visual_hook", ""),
                            "content_strategy": "dedicated_similarity_service",
                            "brand_name": brand_info.get("businessName", "Unknown"),
                            "generation_index": i+1,
                            "provider": "ideogram",
                            "image_generated": image_url is not None,
                            "dimensions": get_platform_dimensions(reference_platform, "educational"),
                            "content_type": post_data.get("content_type", "similar"),
                            "similar_to_reference": True,
                            "reference_template": reference_template,
                            "generation_method": "dedicated_similar_posts_service",
                            "similarity_analysis": post_data.get("similarity_analysis", {}),
                            "reference_used": post_data.get("reference_used", False)
                        }
                    )

                    posts.append(post)
                    logger.info(f"✅ Generated similar post {i+1}/{count}")

                except Exception as e:
                    logger.error(f"❌ Error processing similar post {i+1}: {e}")
                    continue

            if not posts:
                logger.error("❌ No similar posts were generated successfully")
                return PostGenerationResponse(
                    success=False,
                    error="Failed to generate any similar posts",
                    posts=[],
                    total_generated=0
                )

            logger.info(f"🎉 Similar post generation completed: {len(posts)}/{count} posts generated successfully")

            return PostGenerationResponse(
                success=True,
                posts=posts,
                total_generated=len(posts),
                metadata={
                    "generation_type": "similar_posts",
                    "reference_template": reference_template,
                    "reference_platform": reference_platform,
                    "similarity_approach": "improved_content_generation",
                    "brand_name": brand_info.get("businessName", "Unknown")
                }
            )

        except Exception as e:
            logger.error(f"❌ Critical error in similar post generation: {e}")
            return PostGenerationResponse(
                success=False,
                error=f"Similar post generation failed: {str(e)}",
                posts=[],
                total_generated=0
            )

    def _create_clean_background_prompt(self, concept) -> str:
        """
        Create a clean background prompt without text for the two-layer system.

        Args:
            concept: Creative Genius concept with visual_concept and art_direction

        Returns:
            Clean prompt for background image generation (no text)
        """
        # Extract visual elements without text
        visual_concept = concept.visual_concept
        art_direction = getattr(concept, 'art_direction', 'modern')

        # Remove any text-related instructions from the visual concept
        clean_concept = visual_concept.replace('with text that reads', '').replace('text overlay', '').replace('typography', '')

        # Create clean background prompt
        clean_prompt = f"A {art_direction} {clean_concept}, clean design, no text, no typography, no words, professional background for social media"

        logger.info(f"🧹 Created clean background prompt: {clean_prompt[:100]}...")
        return clean_prompt
