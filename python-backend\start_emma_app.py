#!/usr/bin/env python3
import subprocess
import time
import os
import sys

def run_command(command, cwd=None, background=False):
    """Ejecuta un comando en el directorio especificado"""
    print(f"🚀 Ejecutando: {command}")
    if background:
        return subprocess.Popen(command, shell=True, cwd=cwd)
    else:
        result = subprocess.run(command, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ Error ejecutando: {command}")
            print(f"Error: {result.stderr}")
            return False
        print(f"✅ Comando ejecutado exitosamente")
        return True

def main():
    # Directorio base del proyecto
    base_dir = "/Users/<USER>/Desktop/emma-studio--main"
    backend_dir = os.path.join(base_dir, "backend")
    client_dir = os.path.join(base_dir, "client")
    
    print("🎯 Iniciando Emma Studio App")
    print("=" * 50)
    
    # 1. Instalar dependencias del backend si es necesario
    print("\n📦 Verificando dependencias del backend...")
    if not run_command("pip install fastapi uvicorn python-multipart", cwd=backend_dir):
        print("⚠️  Continuando sin instalar dependencias...")
    
    # 2. Iniciar el backend en puerto 8000
    print("\n🔧 Iniciando Backend en puerto 8000...")
    backend_process = run_command("python main.py", cwd=backend_dir, background=True)
    
    if backend_process:
        print("✅ Backend iniciado en segundo plano")
        time.sleep(5)  # Esperar a que el backend se inicie
    else:
        print("❌ Error iniciando el backend")
        return
    
    # 3. Verificar dependencias del frontend
    print("\n📦 Verificando dependencias del frontend...")
    if not run_command("npm install", cwd=client_dir):
        print("⚠️  Continuando sin instalar dependencias del frontend...")
    
    # 4. Iniciar el frontend en puerto 3002
    print("\n💻 Iniciando Frontend en puerto 3002...")
    frontend_process = run_command("npm run dev", cwd=client_dir, background=True)
    
    if frontend_process:
        print("✅ Frontend iniciado en segundo plano")
        time.sleep(3)
    else:
        print("❌ Error iniciando el frontend")
        return
    
    print("\n🎉 Emma Studio App iniciada exitosamente!")
    print("=" * 50)
    print("🔗 Frontend: http://localhost:3002")
    print("🔗 Backend: http://localhost:8000")
    print("🔗 Visual Tools: http://localhost:3002/visual-tools")
    print("🔗 Infographic Creator: http://localhost:3002/infographic-creator")
    print("\n⚠️  Presiona Ctrl+C para detener los servicios")
    
    try:
        # Mantener el script corriendo
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Deteniendo servicios...")
        if backend_process:
            backend_process.terminate()
        if frontend_process:
            frontend_process.terminate()
        print("✅ Servicios detenidos")

if __name__ == "__main__":
    main()
