import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
interface GeneratedResult {
  id: string;
  status: string;
  message: string;
  image_url?: string;
  all_images?: string[];
  num_generated?: number;
  revised_prompt?: string;
  platform?: string;
  size?: string;
  metadata?: any;
}

export function useFreeGeneration() {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [generatedResult, setGeneratedResult] = useState<GeneratedResult | null>(null);
  const [useProductImage, setUseProductImage] = useState(true); // Default to REMIX mode
  const { toast } = useToast();

  const handleFreeGeneration = async () => {
    if (!prompt.trim() && uploadedImages.length === 0) {
      toast({
        title: "¡Necesito algo!",
        description: "Escribe qué quieres o sube una imagen de tu producto",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Llamar al endpoint de generación libre con Ideogram (3 versiones por defecto)
      const formData = new FormData();
      formData.append('prompt', prompt);
      formData.append('platform', 'instagram'); // Default platform
      formData.append('size', '1024x1024'); // Default size
      formData.append('num_images', '3'); // Generar 3 versiones por defecto
      formData.append('use_product_image', useProductImage.toString()); // REMIX vs STYLE mode

      uploadedImages.forEach((image, index) => {
        formData.append(`image_${index}`, image);
      });

      console.log('🚀 Making request to free-generation endpoint...');
      const response = await fetch('/api/v1/ad-creator-agent/free-generation', {
        method: 'POST',
        body: formData
      });

      console.log('📡 Response received:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        ok: response.ok
      });

      if (response.ok) {
        console.log('✅ Response is OK, parsing JSON...');
        const responseText = await response.text();
        console.log('📄 Raw response text length:', responseText.length);
        console.log('📄 Raw response text (first 500 chars):', responseText.substring(0, 500));

        let result;
        try {
          result = JSON.parse(responseText);
          console.log('✅ JSON parsed successfully:', result);
          console.log('🖼️ All images received:', result.all_images);
          console.log('🔢 Number of images:', result.all_images?.length);
          console.log('📊 Num generated:', result.num_generated);
        } catch (parseError) {
          console.error('❌ JSON parse error:', parseError);
          console.error('📄 Full response text:', responseText);
          throw new Error(`Failed to parse response as JSON: ${parseError.message}`);
        }

        toast({
          title: "🎉 ¡Anuncios Generados con Ideogram!",
          description: `Emma creó ${result.num_generated || 3} versiones automáticamente`
        });

        // Mostrar el resultado directamente en la página
        setGeneratedResult(result);
      } else {
        console.log('❌ Response not OK, handling error...');
        // Better error handling for malformed JSON responses
        let errorMessage = 'Error en la generación';
        try {
          const errorText = await response.text();
          console.error('📄 Error response text:', errorText);

          try {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.detail || errorData.message || 'Error en la generación';
          } catch (jsonError) {
            console.error('❌ Failed to parse error response as JSON:', jsonError);
            errorMessage = `Error ${response.status}: ${response.statusText || 'Error del servidor'}`;
            if (errorText && errorText.length < 200) {
              errorMessage += ` - ${errorText}`;
            }
          }
        } catch (textError) {
          console.error('❌ Failed to get error response as text:', textError);
          errorMessage = `Error ${response.status}: ${response.statusText || 'Error del servidor'}`;
        }
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('❌ Caught error in handleFreeGeneration:', error);
      console.error('❌ Error type:', typeof error);
      console.error('❌ Error constructor:', error.constructor.name);
      console.error('❌ Error message:', error.message);
      console.error('❌ Error stack:', error.stack);

      toast({
        title: "Error",
        description: error.message || "Hubo un problema. Inténtalo de nuevo.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const resetGeneration = () => {
    setGeneratedResult(null);
    setPrompt("";
    setUploadedImages([]);
  };

  return {
    prompt,
    setPrompt,
    isGenerating,
    uploadedImages,
    setUploadedImages,
    generatedResult,
    handleFreeGeneration,
    resetGeneration,
    useProductImage,
    setUseProductImage
  };
}
