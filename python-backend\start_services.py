import asyncio
import subprocess
import sys
import os

async def start_qdrant():
    print("🚀 Starting Qdrant Vector Database")
    qdrant_process = subprocess.Popen(
        ["docker", "run", "-p", "6333:6333", "qdrant/qdrant"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    return qdrant_process

async def start_websocket_server():
    print("🌐 Starting WebSocket Server")
    # Ensure the correct path to the WebSocket server script
    websocket_script = os.path.join(
        os.path.dirname(__file__), 
        "server", 
        "src", 
        "services", 
        "agent-protocol-server", 
        "agent-protocol-server.ts"
    )
    
    # Use ts-node to run TypeScript directly
    websocket_process = subprocess.Popen(
        ["npx", "ts-node", websocket_script],
        env={**os.environ, "WEBSOCKET_PORT": "3001"},
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    return websocket_process

async def monitor_processes(processes):
    while True:
        for process in processes:
            poll = process.poll()
            if poll is not None:
                print(f"❌ Process {process.pid} exited with code {poll}")
                stdout, stderr = process.communicate()
                print("STDOUT:", stdout.decode())
                print("STDERR:", stderr.decode())
        await asyncio.sleep(5)

async def main():
    try:
        # Check if Docker is installed and running
        docker_check = subprocess.run(["docker", "info"], capture_output=True, text=True)
        if docker_check.returncode != 0:
            print("❌ Docker is not running. Please start Docker and try again.")
            sys.exit(1)

        # Start services
        qdrant_process = await start_qdrant()
        websocket_process = await start_websocket_server()

        # Wait for services to start
        await asyncio.sleep(10)

        # Monitor processes
        await monitor_processes([qdrant_process, websocket_process])

    except Exception as e:
        print(f"❌ Error starting services: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
