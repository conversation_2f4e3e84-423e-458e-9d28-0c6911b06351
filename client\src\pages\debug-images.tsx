import React from 'react';
import { ImageDisplayTest } from '@/components/debug/ImageDisplayTest';
import { ImageDebug } from '@/components/debug/ImageDebug';

export default function DebugImagesPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Image Display Debug Tool</h1>
        <p className="text-gray-600 mb-8">
          This tool helps diagnose image display issues in Emma Studio.
          Use it to test the complete image upload, storage, and retrieval flow.
        </p>

        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-4">Platform Features Images Test</h2>
          <ImageDebug />
        </div>

        <div>
          <h2 className="text-2xl font-bold mb-4">General Image Display Test</h2>
          <ImageDisplayTest />
        </div>
      </div>
    </div>
  );
}
