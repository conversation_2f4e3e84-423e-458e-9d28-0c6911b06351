// vite.config.js
// ----------------------------------------------------------
// Archivo de configuración de Vite para el entorno de desarrollo.
// Este archivo define reglas para el servidor de desarrollo, como proxies.
//
// - El proxy '/uploads' permite que las peticiones a rutas que comiencen con /uploads
//   sean redirigidas al backend local (usualmente para servir archivos estáticos subidos).
// - El proxy '/api' redirige las llamadas a la API al backend local, facilitando el desarrollo
//   sin problemas de CORS.
//
// Ambas rutas se utilizan extensamente en el frontend y servicios del proyecto.
// ----------------------------------------------------------
import { defineConfig } from 'vite';

export default defineConfig({
  server: {
    proxy: {
      // Proxy para servir archivos subidos desde el backend
      '/uploads': 'http://localhost:8000',
      // Proxy para llamadas a la API del backend
      '/api': 'http://localhost:8000',
    }
  }
});
