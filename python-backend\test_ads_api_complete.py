#!/usr/bin/env python3
"""
Complete End-to-End Test for Emma Studio Ads Creator API
Tests the full workflow from frontend to backend to Ideogram API
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, Any

# Test configuration
BACKEND_URL = "http://localhost:8000"
API_KEY = "dev-api-key-for-testing"

async def test_health_check():
    """Test if the backend is running and healthy."""
    print("🏥 Testing health check...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BACKEND_URL}/api/v1/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Backend is healthy: {data}")
                    return True
                else:
                    print(f"❌ Health check failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False

async def test_single_ad_generation():
    """Test generating a single ad."""
    print("\n🎨 Testing single ad generation...")
    
    # Test data
    form_data = aiohttp.FormData()
    form_data.add_field('prompt', 'Create a professional advertisement for premium protein powder for athletes')
    form_data.add_field('size', '1024x1024')
    
    headers = {
        'X-API-Key': API_KEY
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            start_time = time.time()
            async with session.post(
                f"{BACKEND_URL}/api/ads/generate",
                data=form_data,
                headers=headers
            ) as response:
                end_time = time.time()
                
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Ad generated successfully in {end_time - start_time:.2f}s")
                    print(f"   Image URL: {data.get('image_url', 'N/A')[:80]}...")
                    print(f"   Model: {data.get('metadata', {}).get('model', 'N/A')}")
                    return data
                else:
                    error_text = await response.text()
                    print(f"❌ Ad generation failed: {response.status}")
                    print(f"   Error: {error_text}")
                    return None
        except Exception as e:
            print(f"❌ Ad generation error: {e}")
            return None

async def test_parallel_generation_simulation():
    """Simulate parallel generation like our new system."""
    print("\n⚡ Testing parallel generation simulation...")
    
    # Create 3 concurrent requests (like our batched system)
    tasks = []
    for i in range(3):
        form_data = aiohttp.FormData()
        form_data.add_field('prompt', f'Professional ad for premium protein powder - variation {i+1}')
        form_data.add_field('size', '1024x1024')
        
        headers = {'X-API-Key': API_KEY}
        
        task = generate_single_ad_async(form_data, headers, i+1)
        tasks.append(task)
    
    start_time = time.time()
    results = await asyncio.gather(*tasks, return_exceptions=True)
    end_time = time.time()
    
    successful = [r for r in results if isinstance(r, dict) and r.get('success')]
    failed = [r for r in results if not (isinstance(r, dict) and r.get('success'))]
    
    print(f"✅ Parallel generation completed in {end_time - start_time:.2f}s")
    print(f"   Successful: {len(successful)}/3")
    print(f"   Failed: {len(failed)}/3")
    
    return successful

async def generate_single_ad_async(form_data, headers, variation_num):
    """Helper function for parallel generation."""
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                f"{BACKEND_URL}/api/ads/generate",
                data=form_data,
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ Variation {variation_num} completed")
                    return data
                else:
                    print(f"   ❌ Variation {variation_num} failed: {response.status}")
                    return {"success": False, "error": f"HTTP {response.status}"}
        except Exception as e:
            print(f"   ❌ Variation {variation_num} error: {e}")
            return {"success": False, "error": str(e)}

async def test_with_reference_image():
    """Test ad generation with reference image."""
    print("\n📷 Testing ad generation with reference image...")
    
    # Create a simple test image (1x1 pixel PNG)
    test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\x0cIDATx\x9cc```\x00\x00\x00\x04\x00\x01\xdd\x8d\xb4\x1c\x00\x00\x00\x00IEND\xaeB`\x82'
    
    form_data = aiohttp.FormData()
    form_data.add_field('prompt', 'Create a professional advertisement based on this reference')
    form_data.add_field('size', '1024x1024')
    form_data.add_field('reference_images', test_image_data, filename='test.png', content_type='image/png')
    
    headers = {'X-API-Key': API_KEY}
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                f"{BACKEND_URL}/api/ads/edit-with-references",
                data=form_data,
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Reference-based ad generated successfully")
                    print(f"   Image URL: {data.get('image_url', 'N/A')[:80]}...")
                    return data
                else:
                    error_text = await response.text()
                    print(f"❌ Reference-based generation failed: {response.status}")
                    print(f"   Error: {error_text}")
                    return None
        except Exception as e:
            print(f"❌ Reference-based generation error: {e}")
            return None

async def main():
    """Run all tests."""
    print("🧪 Emma Studio Ads Creator - Complete API Test")
    print("=" * 60)
    
    # Test 1: Health check
    if not await test_health_check():
        print("❌ Backend is not running. Please start it first.")
        return
    
    # Test 2: Single ad generation
    single_result = await test_single_ad_generation()
    
    # Test 3: Parallel generation simulation
    parallel_results = await test_parallel_generation_simulation()
    
    # Test 4: Reference image generation
    reference_result = await test_with_reference_image()
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 60)
    print(f"✅ Health Check: {'PASS' if True else 'FAIL'}")
    print(f"✅ Single Generation: {'PASS' if single_result else 'FAIL'}")
    print(f"✅ Parallel Generation: {'PASS' if len(parallel_results) > 0 else 'FAIL'}")
    print(f"✅ Reference Generation: {'PASS' if reference_result else 'FAIL'}")
    
    if single_result and len(parallel_results) > 0:
        print("\n🎉 All core functionality is working!")
        print("   - Backend API is running correctly")
        print("   - Ideogram integration is functional")
        print("   - Both single and parallel generation work")
        print("   - The ads creator should work perfectly in the frontend")
    else:
        print("\n⚠️  Some tests failed. Check the logs above.")

if __name__ == "__main__":
    asyncio.run(main())
