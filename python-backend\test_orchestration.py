#!/usr/bin/env python3
"""
Test script to verify the Emma orchestration system is working correctly.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_orchestration():
    """Test the Emma orchestration system."""
    try:
        from app.services.agent_service import agent_service
        from app.schemas.crew import AgentChatRequest
        
        print("🎯 Testing Emma Orchestration System")
        print("=" * 50)
        
        # Test 1: Simple greeting
        print("\n📝 Test 1: Simple greeting")
        request = AgentChatRequest(
            agent_id="emma",
            message="Hello Emma! How are you?",
            context={}
        )
        
        response = await agent_service.chat_with_agent(request)
        print(f"✅ Response: {response.response[:100]}...")
        print(f"📊 Reasoning steps: {len(response.reasoning_trace)}")
        
        # Test 2: Content request (should trigger coordination)
        print("\n📝 Test 2: Content creation request")
        request = AgentChatRequest(
            agent_id="content",  # User requests content agent, but <PERSON> should orchestrate
            message="Help me create a blog post about AI in marketing",
            context={}
        )
        
        response = await agent_service.chat_with_agent(request)
        print(f"✅ Response: {response.response[:100]}...")
        print(f"📊 Reasoning steps: {len(response.reasoning_trace)}")
        
        # Check if delegation occurred
        delegation_steps = [step for step in response.reasoning_trace if step.get('type') == 'delegation']
        if delegation_steps:
            print(f"🤝 Delegation detected: {len(delegation_steps)} steps")
        else:
            print("ℹ️ No delegation steps found (Emma handled directly)")
        
        # Test 3: SEO request
        print("\n📝 Test 3: SEO optimization request")
        request = AgentChatRequest(
            agent_id="seo",  # User requests SEO agent, but Emma should orchestrate
            message="Analyze the SEO potential for a tech startup website",
            context={}
        )
        
        response = await agent_service.chat_with_agent(request)
        print(f"✅ Response: {response.response[:100]}...")
        print(f"📊 Reasoning steps: {len(response.reasoning_trace)}")
        
        # Check reasoning trace
        for i, step in enumerate(response.reasoning_trace):
            print(f"  Step {i+1}: {step.get('type', 'unknown')} - {step.get('content', '')[:50]}...")
        
        print("\n🎉 All tests completed successfully!")
        print("✅ Emma orchestration system is working correctly")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_orchestration())
