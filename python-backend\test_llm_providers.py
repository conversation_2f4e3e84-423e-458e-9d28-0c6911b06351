#!/usr/bin/env python
"""
Test script for the LLM providers.
This script tests the Gemini and OpenAI providers.
"""

import os
import sys
import asyncio
import logging
from typing import Dict, Any, Optional, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

from agents.llm_providers import GeminiProvider, OpenAIProvider

async def test_gemini_provider():
    """Test the Gemini provider."""
    # Get the API key from the environment
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key:
        logger.warning("No Gemini API key found. Skipping Gemini provider test.")
        return False
    
    # Initialize the provider
    provider = GeminiProvider(api_key=api_key)
    
    # Test the provider
    try:
        logger.info("Testing Gemini provider...")
        response = await provider.generate("Hello, world!")
        logger.info(f"Gemini response: {response}")
        return True
    except Exception as e:
        logger.error(f"Error testing Gemini provider: {e}")
        return False

async def test_openai_provider():
    """Test the OpenAI provider."""
    # Get the API key from the environment
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        logger.warning("No OpenAI API key found. Skipping OpenAI provider test.")
        return False
    
    # Initialize the provider
    provider = OpenAIProvider(api_key=api_key)
    
    # Test the provider
    try:
        logger.info("Testing OpenAI provider...")
        response = await provider.generate("Hello, world!")
        logger.info(f"OpenAI response: {response}")
        return True
    except Exception as e:
        logger.error(f"Error testing OpenAI provider: {e}")
        return False

async def run_tests():
    """Run all tests."""
    # Test the providers
    gemini_success = await test_gemini_provider()
    openai_success = await test_openai_provider()
    
    # Check if at least one provider is working
    if gemini_success or openai_success:
        logger.info("At least one provider is working!")
        return True
    else:
        logger.error("No providers are working!")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_tests())
    sys.exit(0 if success else 1)
