<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - <PERSON>'s perfect cakes</title>
    <meta name="description" content="Get in touch with <PERSON>'s perfect cakes for custom orders, inquiries, or quotes.">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header class="site-header">
        <div class="container header-container">
            <a href="index.html" class="brand-logo"><PERSON>'s perfect cakes</a>
            <nav class="main-nav" id="main-nav">
                 <button class="nav-toggle" aria-label="Toggle navigation" aria-expanded="false">
                    <span class="hamburger"></span>
                </button>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="gallery.html">Gallery & Shop</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html" class="active">Contact</a></li>
                     <!-- Optional: Placeholder Cart Icon
                    <li><a href="#" class="cart-icon" aria-label="Shopping Cart">(0)</a></li>
                    -->
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <section class="page-header section-padding">
            <div class="container">
                <h1>Get In Touch</h1>
                <p>[Have a question or ready to order your dream cake? Contact us below!]</p>
            </div>
        </section>

        <section class="contact-section section-padding">
            <div class="container contact-container">
                <div class="contact-form">
                    <h2>Send Us a Message</h2>
                    <p>[Use this form for inquiries about custom cakes, availability, or general questions.]</p>
                    <!-- NOTE: This form requires backend processing to actually send emails. -->
                    <!-- TODO: Set up a form processing service (like Netlify Forms, Formspree) or backend script -->
                    <form id="contact-form" action="#" method="POST"> <!-- Replace # with your form processing endpoint -->
                        <div class="form-group">
                            <label for="name">Name:</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email:</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="subject">Subject / Cake Type:</label>
                            <input type="text" id="subject" name="subject" required placeholder="e.g., Wedding Cake Inquiry, Birthday Cake Order">
                        </div>
                         <div class="form-group">
                            <label for="event-date">Date of Event (Optional):</label>
                            <input type="date" id="event-date" name="event-date">
                        </div>
                        <div class="form-group">
                            <label for="message">Message:</label>
                            <textarea id="message" name="message" rows="6" required placeholder="Please provide details like number of servings needed, flavor ideas, design concepts..."></textarea>
                        </div>
                        <button type="submit" class="cta-button">Send Message</button>
                    </form>
                     <p id="form-status" class="form-status"></p> <!-- For displaying submission status messages (needs JS) -->
                </div>

                <div class="contact-details">
                    <h2>Contact Information</h2>
                     <!-- TODO: Replace placeholders with actual contact details -->
                    <p><strong>Address:</strong><br> [Your Street Address]<br>[Your City, State, Zip Code]<br>(Visits by appointment only)</p>
                    <p><strong>Phone:</strong> <a href="tel:+1234567890">[Your Phone Number]</a></p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>">[Your Email Address e.g., <EMAIL>]</a></p>

                    <h3>Business Hours</h3>
                     <!-- TODO: Update with actual business hours -->
                    <p>Tuesday - Friday: 10:00 AM - 6:00 PM</p>
                    <p>Saturday: 10:00 AM - 4:00 PM</p>
                    <p>Sunday & Monday: Closed</p>

                    <!-- Optional: Google Maps Embed -->
                    <!-- <div class="map-placeholder">
                        <img src="https://via.placeholder.com/400x300.png?text=Map+Placeholder" alt="Map Placeholder">
                        <! -- TODO: Replace with an embedded Google Map iframe -- >
                    </div> -->
                </div>
            </div>
        </section>
    </main>

    <footer class="site-footer">
        <div class="container">
            <p>&copy; <span id="current-year"></span> Antonio's perfect cakes. All Rights Reserved.</p>
            <!-- Optional: Add social media links here -->
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>