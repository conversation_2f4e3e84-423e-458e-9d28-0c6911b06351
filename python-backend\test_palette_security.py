#!/usr/bin/env python3
"""
Security test script for user palette RLS policies in Supabase.
This script verifies that Row Level Security (RLS) policies work correctly
and users can only access their own palettes.
"""

import asyncio
import json
import sys
from typing import Dict, Any, List
import httpx
import uuid

# Test configuration
BASE_URL = "http://localhost:5001"  # Backend URL
TEST_USERS = [
    {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "name": "Test User 1"
    },
    {
        "email": "<EMAIL>", 
        "password": "testpassword123",
        "name": "Test User 2"
    }
]

class PaletteSecurityTester:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self.user_tokens = {}
        self.user_palettes = {}
        
    async def cleanup(self):
        """Clean up HTTP client"""
        await self.client.aclose()
    
    async def authenticate_user(self, user_data: Dict[str, str]) -> str:
        """Authenticate a user and return their token"""
        try:
            # Try to login first
            login_response = await self.client.post(
                f"{BASE_URL}/api/auth/login",
                json={
                    "username": user_data["email"],
                    "password": user_data["password"]
                }
            )
            
            if login_response.status_code == 200:
                token = login_response.json().get("access_token")
                print(f"✅ User {user_data['email']} logged in successfully")
                return token
            else:
                print(f"❌ Login failed for {user_data['email']}: {login_response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Authentication error for {user_data['email']}: {str(e)}")
            return None
    
    async def create_test_palette(self, token: str, user_email: str) -> Dict[str, Any]:
        """Create a test palette for a user"""
        palette_data = {
            "name": f"Test Palette for {user_email}",
            "colors": ["#FF5733", "#33FF57", "#3357FF", "#F39C12", "#9B59B6"],
            "description": f"Test palette created for security testing - {user_email}",
            "tags": ["test", "security"],
            "is_favorite": False
        }
        
        try:
            response = await self.client.post(
                f"{BASE_URL}/api/palettes",
                json=palette_data,
                headers={"Authorization": f"Bearer {token}"}
            )
            
            if response.status_code == 200:
                palette = response.json().get("palette")
                print(f"✅ Created test palette for {user_email}: {palette['id']}")
                return palette
            else:
                print(f"❌ Failed to create palette for {user_email}: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error creating palette for {user_email}: {str(e)}")
            return None
    
    async def test_user_can_access_own_palettes(self, token: str, user_email: str) -> bool:
        """Test that a user can access their own palettes"""
        try:
            response = await self.client.get(
                f"{BASE_URL}/api/palettes",
                headers={"Authorization": f"Bearer {token}"}
            )
            
            if response.status_code == 200:
                palettes = response.json().get("palettes", [])
                print(f"✅ User {user_email} can access their palettes ({len(palettes)} found)")
                return True
            else:
                print(f"❌ User {user_email} cannot access their palettes: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error accessing palettes for {user_email}: {str(e)}")
            return False
    
    async def test_user_cannot_access_other_palettes(self, token: str, user_email: str, other_palette_id: str) -> bool:
        """Test that a user cannot access another user's palette"""
        try:
            response = await self.client.get(
                f"{BASE_URL}/api/palettes/{other_palette_id}",
                headers={"Authorization": f"Bearer {token}"}
            )
            
            if response.status_code == 404:
                print(f"✅ User {user_email} correctly cannot access other user's palette")
                return True
            elif response.status_code == 403:
                print(f"✅ User {user_email} correctly forbidden from accessing other user's palette")
                return True
            else:
                print(f"❌ SECURITY ISSUE: User {user_email} can access other user's palette: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing cross-user access for {user_email}: {str(e)}")
            return False
    
    async def test_user_cannot_modify_other_palettes(self, token: str, user_email: str, other_palette_id: str) -> bool:
        """Test that a user cannot modify another user's palette"""
        update_data = {
            "name": "HACKED PALETTE NAME",
            "description": "This should not work!"
        }
        
        try:
            response = await self.client.put(
                f"{BASE_URL}/api/palettes/{other_palette_id}",
                json=update_data,
                headers={"Authorization": f"Bearer {token}"}
            )
            
            if response.status_code in [404, 403]:
                print(f"✅ User {user_email} correctly cannot modify other user's palette")
                return True
            else:
                print(f"❌ SECURITY ISSUE: User {user_email} can modify other user's palette: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing cross-user modification for {user_email}: {str(e)}")
            return False
    
    async def test_user_cannot_delete_other_palettes(self, token: str, user_email: str, other_palette_id: str) -> bool:
        """Test that a user cannot delete another user's palette"""
        try:
            response = await self.client.delete(
                f"{BASE_URL}/api/palettes/{other_palette_id}",
                headers={"Authorization": f"Bearer {token}"}
            )
            
            if response.status_code in [404, 403]:
                print(f"✅ User {user_email} correctly cannot delete other user's palette")
                return True
            else:
                print(f"❌ SECURITY ISSUE: User {user_email} can delete other user's palette: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing cross-user deletion for {user_email}: {str(e)}")
            return False
    
    async def test_unauthenticated_access(self) -> bool:
        """Test that unauthenticated users cannot access palettes"""
        try:
            response = await self.client.get(f"{BASE_URL}/api/palettes")
            
            if response.status_code == 401:
                print("✅ Unauthenticated users correctly cannot access palettes")
                return True
            else:
                print(f"❌ SECURITY ISSUE: Unauthenticated users can access palettes: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing unauthenticated access: {str(e)}")
            return False
    
    async def run_comprehensive_security_test(self) -> bool:
        """Run comprehensive security tests"""
        print("🔒 Starting Comprehensive Palette Security Tests")
        print("=" * 60)
        
        all_tests_passed = True
        
        # Test 1: Unauthenticated access
        print("\n📋 Test 1: Unauthenticated Access")
        if not await self.test_unauthenticated_access():
            all_tests_passed = False
        
        # Authenticate test users
        print("\n📋 Test 2: User Authentication")
        for user in TEST_USERS:
            token = await self.authenticate_user(user)
            if token:
                self.user_tokens[user["email"]] = token
            else:
                print(f"❌ Cannot proceed with tests - authentication failed for {user['email']}")
                return False
        
        # Create test palettes for each user
        print("\n📋 Test 3: Create Test Palettes")
        for user in TEST_USERS:
            email = user["email"]
            token = self.user_tokens[email]
            palette = await self.create_test_palette(token, email)
            if palette:
                self.user_palettes[email] = palette
            else:
                print(f"❌ Cannot proceed with tests - palette creation failed for {email}")
                return False
        
        # Test 4: Users can access their own palettes
        print("\n📋 Test 4: Own Palette Access")
        for user in TEST_USERS:
            email = user["email"]
            token = self.user_tokens[email]
            if not await self.test_user_can_access_own_palettes(token, email):
                all_tests_passed = False
        
        # Test 5: Users cannot access other users' palettes
        print("\n📋 Test 5: Cross-User Palette Access (Should Fail)")
        user1_email = TEST_USERS[0]["email"]
        user2_email = TEST_USERS[1]["email"]
        user1_token = self.user_tokens[user1_email]
        user2_token = self.user_tokens[user2_email]
        user1_palette_id = self.user_palettes[user1_email]["id"]
        user2_palette_id = self.user_palettes[user2_email]["id"]
        
        if not await self.test_user_cannot_access_other_palettes(user2_token, user2_email, user1_palette_id):
            all_tests_passed = False
        if not await self.test_user_cannot_access_other_palettes(user1_token, user1_email, user2_palette_id):
            all_tests_passed = False
        
        # Test 6: Users cannot modify other users' palettes
        print("\n📋 Test 6: Cross-User Palette Modification (Should Fail)")
        if not await self.test_user_cannot_modify_other_palettes(user2_token, user2_email, user1_palette_id):
            all_tests_passed = False
        if not await self.test_user_cannot_modify_other_palettes(user1_token, user1_email, user2_palette_id):
            all_tests_passed = False
        
        # Test 7: Users cannot delete other users' palettes
        print("\n📋 Test 7: Cross-User Palette Deletion (Should Fail)")
        if not await self.test_user_cannot_delete_other_palettes(user2_token, user2_email, user1_palette_id):
            all_tests_passed = False
        if not await self.test_user_cannot_delete_other_palettes(user1_token, user1_email, user2_palette_id):
            all_tests_passed = False
        
        # Summary
        print("\n" + "=" * 60)
        if all_tests_passed:
            print("🎉 ALL SECURITY TESTS PASSED!")
            print("✅ Row Level Security (RLS) policies are working correctly")
            print("✅ Users can only access their own palettes")
            print("✅ Cross-user access is properly blocked")
        else:
            print("🚨 SECURITY TESTS FAILED!")
            print("❌ There are security vulnerabilities in the RLS policies")
            print("❌ Manual review and fixes are required")
        
        return all_tests_passed

async def main():
    """Main test function"""
    tester = PaletteSecurityTester()
    
    try:
        success = await tester.run_comprehensive_security_test()
        sys.exit(0 if success else 1)
    finally:
        await tester.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
