"use client"

import { Feature72 } from "@/components/ui/feature72"
import { SubtleGradient } from "@/components/ui/subtle-gradients"
import { useLanguage } from "@/contexts/LanguageContext"

export function PlatformFeatures() {
  const { t } = useLanguage()

  const emmaFeatures = [
    {
      id: "marketplace-agentes",
      title: t('landing.marketplace_agents'),
      description: t('landing.marketplace_description'),
      image: "/Agent Marketplace.png"
    },
    {
      id: "content-builder",
      title: t('landing.editor'),
      description: t('landing.editor_description'),
      image: "/Editor.png"
    },
    {
      id: "emma-ai",
      title: t('landing.emma_ai'),
      description: t('landing.emma_ai_description'),
      image: "/Emma.png"
    },
    {
      id: "visual-studio",
      title: t('landing.visual_studio'),
      description: t('landing.visual_studio_description'),
      image: "/Visual Studio.png"
    },
    {
      id: "herramientas-marketing",
      title: t('landing.marketing_tools'),
      description: t('landing.marketing_tools_description'),
      image: "/Herramientas.png"
    },
    {
      id: "ads-central",
      title: t('landing.ads_central'),
      description: t('landing.ads_central_description'),
      image: "/Ads Central.png"
    }
  ]

  return (
    <div className="relative">
      <SubtleGradient variant="gradient" position="top-right" size="lg" />
      <Feature72
        heading={t('landing.platform_heading')}
        description={t('landing.platform_description')}
        linkUrl="/register"
        linkText={t('landing.start_now')}
        features={emmaFeatures}
      />
    </div>
  )
}