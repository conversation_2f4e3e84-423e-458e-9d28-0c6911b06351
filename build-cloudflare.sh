#!/bin/bash

# Cloudflare Pages build script - Frontend only
# This script ensures only Node.js/frontend dependencies are used

echo "🚀 Starting Emma Studio frontend build for Cloudflare Pages..."

# Set Node.js version
export NODE_VERSION="18.20.8"

# Disable Python/Poetry detection
export DISABLE_POETRY=true
export DISABLE_PYTHON=true
export NODE_ENV=production

# Navigate to client directory
cd client

echo "📦 Installing Node.js dependencies..."
npm ci --production=false

echo "🏗️ Building frontend application..."
npm run build

echo "✅ Build completed successfully!"
echo "📁 Build output directory: client/dist"

# Verify build output exists
if [ -d "dist" ]; then
    echo "✅ Build output verified - dist directory exists"
    ls -la dist/
else
    echo "❌ Build failed - dist directory not found"
    exit 1
fi
