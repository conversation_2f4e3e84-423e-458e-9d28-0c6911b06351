#!/usr/bin/env python3
"""
Test script to verify color palette saving functionality
"""
import requests
import json

# Test data
test_palette = {
    "name": "Test Palette Fix",
    "colors": ["#FF5733", "#33FF57", "#3357FF", "#F39C12"],
    "description": "Testing the palette save fix",
    "tags": ["test", "fix"],
    "is_favorite": False
}

def test_save_palette():
    """Test saving a color palette"""
    print("🎨 Testing Color Palette Save...")
    
    # Make the API call
    url = "http://localhost:5001/api/palettes"
    headers = {
        "Content-Type": "application/json",
        # Note: In a real scenario, you'd need proper authentication headers
    }
    
    try:
        response = requests.post(url, json=test_palette, headers=headers)
        
        print(f"📡 Response Status: {response.status_code}")
        print(f"📡 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Palette saved successfully!")
            print(f"📄 Response: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"❌ Failed to save palette: {response.status_code}")
            print(f"📄 Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Network error: {e}")
        return False

if __name__ == "__main__":
    success = test_save_palette()
    if success:
        print("🎉 Test completed successfully!")
    else:
        print("💥 Test failed!")
