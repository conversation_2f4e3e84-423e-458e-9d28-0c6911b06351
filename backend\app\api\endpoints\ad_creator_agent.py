"""
API endpoints for the Ad Creator Agent.
Specialized agent for ad creation workflows that feels like <PERSON>.
"""

import logging
import time
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Form, File, UploadFile
from pydantic import BaseModel, Field
from app.services.ad_creator_agent_service import AdCreatorAgentService
from app.services.ideogram_service import IdeogramService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/ad-creator-agent", tags=["Ad Creator Agent"])

# Initialize services
ad_creator_agent = AdCreatorAgentService()
ideogram_service = IdeogramService()


class AdCreatorChatRequest(BaseModel):
    """Request model for ad creator agent chat."""
    message: str = Field(..., description="User's message to the ad creator agent")
    context: Optional[Dict[str, Any]] = Field(default=None, description="Context including platform, current state, etc.")


class AdCreatorChatResponse(BaseModel):
    """Response model for ad creator agent chat."""
    response: str = Field(..., description="Agent's response")
    suggestions: list[str] = Field(default=[], description="Contextual suggestions for the user")
    field_suggestions: Dict[str, str] = Field(default={}, description="Suggestions for form field population")
    metadata: Dict[str, Any] = Field(default={}, description="Additional metadata")


class GenerateFieldRequest(BaseModel):
    """Request model for generating specific fields."""
    field_type: str = Field(..., description="Type of field to generate (headline, punchline, cta)")
    platform: str = Field(..., description="Platform for the ad (facebook, instagram, linkedin, etc.)")
    product_description: str = Field(..., description="Description of the product/service")


class GenerateFieldResponse(BaseModel):
    """Response model for field generation."""
    content: str = Field(..., description="Generated content for the field")
    field_type: str = Field(..., description="Type of field that was generated")
    platform: str = Field(..., description="Platform the content was optimized for")


@router.post("/chat", response_model=AdCreatorChatResponse)
async def chat_with_ad_creator_agent(request: AdCreatorChatRequest):
    """
    Chat with the specialized ad creator agent.
    This agent feels like Emma but is focused on ad creation workflows.
    """
    try:
        logger.info(f"Ad creator agent chat request: {request.message[:100]}...")

        # Process the chat request
        result = await ad_creator_agent.chat(
            message=request.message,
            context=request.context
        )

        return AdCreatorChatResponse(
            response=result["response"],
            suggestions=result.get("suggestions", []),
            field_suggestions=result.get("field_suggestions", {}),
            metadata=result.get("metadata", {})
        )

    except Exception as e:
        logger.error(f"Error in ad creator agent chat: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error processing chat request: {str(e)}"
        )


@router.post("/intelligent-chat", response_model=AdCreatorChatResponse)
async def intelligent_chat_with_emma(request: AdCreatorChatRequest):
    """
    Intelligent chat with Emma - uses advanced AI instead of hardcoded responses.
    This is the improved version that replaces the "pendejo" chatbot.
    """
    try:
        logger.info(f"Emma intelligent chat request: {request.message[:100]}...")

        # Enhanced context for intelligent responses
        enhanced_context = request.context or {}
        enhanced_context.update({
            "mode": "intelligent_chat",
            "personality": "emma_smart",
            "capabilities": [
                "analizar_negocio_inteligente",
                "sugerir_prompts_contextuales",
                "optimizar_anuncios_avanzado",
                "generar_headlines_creativos",
                "crear_punchlines_persuasivos",
                "sugerir_ctas_efectivos",
                "recomendar_estrategias_personalizadas"
            ]
        })

        # Use the intelligent chat method
        result = await ad_creator_agent.intelligent_chat(
            message=request.message,
            context=enhanced_context
        )

        return AdCreatorChatResponse(
            response=result["response"],
            suggestions=result.get("suggestions", []),
            field_suggestions=result.get("field_suggestions", {}),
            metadata=result.get("metadata", {})
        )

    except Exception as e:
        logger.error(f"Error in Emma intelligent chat: {e}")
        # Fallback to smart response instead of generic error
        smart_fallback = await ad_creator_agent.generate_smart_fallback(
            message=request.message,
            context=request.context
        )

        return AdCreatorChatResponse(
            response=smart_fallback["response"],
            suggestions=smart_fallback.get("suggestions", []),
            field_suggestions={},
            metadata={"fallback": True}
        )


def translate_prompt_to_english(spanish_prompt: str) -> str:
    """
    Traduce prompts del español al inglés para Ideogram.
    Ideogram funciona mejor con prompts en inglés.
    """
    if not spanish_prompt or spanish_prompt.strip() == "":
        return spanish_prompt

    # Diccionario de traducciones específicas para marketing/anuncios
    translations = {
        # Productos y servicios
        "suplemento": "supplement",
        "natural": "natural",
        "dolor": "pain",
        "cabeza": "headache",
        "app": "app",
        "aplicación": "application",
        "móvil": "mobile",
        "fitness": "fitness",
        "ropa": "clothing",
        "deportiva": "sportswear",
        "premium": "premium",
        "curso": "course",
        "online": "online",
        "marketing": "marketing",
        "digital": "digital",
        "restaurante": "restaurant",
        "comida": "food",
        "saludable": "healthy",
        "servicio": "service",
        "consultoría": "consulting",
        "empresarial": "business",
        "saas": "saas",
        "software": "software",
        "plataforma": "platform",
        "agentes": "agents",
        "ia": "ai",
        "inteligencia artificial": "artificial intelligence",
        "autónomos": "autonomous",
        "suscribirse": "subscribe",
        "suscripción": "subscription",
        "anuncio": "advertisement",
        "invitando": "inviting",
        "gente": "people",

        # Descriptivos de marketing
        "profesional": "professional",
        "innovador": "innovative",
        "moderno": "modern",
        "elegante": "elegant",
        "efectivo": "effective",
        "rápido": "fast",
        "fácil": "easy",
        "mejor": "best",
        "nuevo": "new",
        "exclusivo": "exclusive",
        "calidad": "quality",
        "precio": "price",
        "oferta": "offer",
        "descuento": "discount",
        "gratis": "free",

        # Acciones
        "comprar": "buy",
        "obtener": "get",
        "descargar": "download",
        "probar": "try",
        "usar": "use",
        "funciona": "works",
        "ayuda": "helps",
        "mejora": "improves",

        # Tiempo
        "minutos": "minutes",
        "horas": "hours",
        "días": "days",
        "semanas": "weeks",
        "meses": "months",
        "años": "years",
        "ahora": "now",
        "hoy": "today",
        "mañana": "tomorrow"
    }

    # Frases completas comunes en marketing
    phrase_translations = {
        "dolor de cabeza": "headache",
        "app móvil": "mobile app",
        "ropa deportiva": "sportswear",
        "marketing digital": "digital marketing",
        "comida saludable": "healthy food",
        "consultoría empresarial": "business consulting",
        "funciona en": "works in",
        "resultados en": "results in",
        "sin efectos secundarios": "no side effects",
        "100% natural": "100% natural",
        "fácil de usar": "easy to use",
        "alta calidad": "high quality",
        "agentes de ia": "ai agents",
        "agentes de ia autónomos": "autonomous ai agents",
        "saas de agentes": "ai agents saas",
        "inteligencia artificial": "artificial intelligence",
        "suscribirse a mi saas": "subscribe to my saas",
        "invitando a la gente": "inviting people",
        "un anuncio invitando": "an advertisement inviting"
    }

    translated_prompt = spanish_prompt.lower()

    # Aplicar traducciones de frases completas primero
    for spanish_phrase, english_phrase in phrase_translations.items():
        if spanish_phrase in translated_prompt:
            translated_prompt = translated_prompt.replace(spanish_phrase, english_phrase)

    # Luego aplicar traducciones palabra por palabra
    import re
    for spanish_word, english_word in translations.items():
        pattern = r"\b" + re.escape(spanish_word) + r"\b"
        translated_prompt = re.sub(pattern, english_word, translated_prompt, flags=re.IGNORECASE)

    # Limpiar espacios múltiples y capitalizar
    translated_prompt = re.sub(r"\s+", " ", translated_prompt).strip()
    if translated_prompt:
        translated_prompt = translated_prompt[0].upper() + translated_prompt[1:]

    logger.info(f"🌐 Prompt traducido: '{spanish_prompt}' → '{translated_prompt}'")
    return translated_prompt


@router.post("/free-generation")
async def free_generation(
    prompt: str = Form(..., description="Descripción del producto o servicio a promocionar"),
    platform: str = Form(default="instagram", description="Plataforma objetivo (instagram, facebook, etc.)"),
    size: str = Form(default="1024x1024", description="Tamaño de la imagen (1024x1024, 1024x1792, 1792x1024)"),
    num_images: int = Form(default=3, description="Número de versiones a generar (1-4, default 3)"),
    use_product_image: bool = Form(default=False, description="Si es True, incorpora la imagen como producto real (REMIX). Si es False, usa solo como referencia de estilo"),
    image_0: UploadFile = File(None, description="Imagen de referencia opcional"),
    image_1: UploadFile = File(None, description="Segunda imagen de referencia opcional"),
    image_2: UploadFile = File(None, description="Tercera imagen de referencia opcional")
):
    """
    Generación libre como Ideogram - directo al grano.

    El usuario describe qué quiere promocionar y Emma genera automáticamente
    un anuncio profesional usando Ideogram AI con las mejores prácticas:

    - Traduce automáticamente prompts al inglés
    - Usa Ideogram 3.0 Quality para mejor calidad de texto
    - Aplica magic_prompt AUTO para mejores resultados
    - Usa style_type DESIGN para anuncios profesionales
    - Incluye negative_prompt optimizado para marketing

    Args:
        prompt: Descripción del producto/servicio (ej: "Suplemento natural para dolor de cabeza")
        platform: Plataforma objetivo para optimización
        size: Dimensiones de la imagen
        image_0-2: Imágenes de referencia opcionales

    Returns:
        Respuesta con imagen generada y metadatos completos
    """
    try:
        logger.info(f"🎲 Free generation request: {prompt[:100]}... (generating {num_images} versions)")

        # Validar número de imágenes
        if num_images < 1 or num_images > 4:
            num_images = 3
            logger.warning(f"Invalid num_images, using default: {num_images}")

        # Traducir prompt al inglés (Ideogram funciona mejor en inglés)
        english_prompt = translate_prompt_to_english(prompt)

        # Crear prompt optimizado para anuncios profesionales con mejores prácticas Ideogram
        enhanced_prompt = f"""Create a professional marketing advertisement for: {english_prompt}.
Design requirements: High-impact visual design, bold readable typography, modern minimalist layout,
premium brand aesthetic, compelling call-to-action, professional color scheme, clean composition,
commercial photography style, marketing-focused design, conversion-optimized layout, premium quality finish"""

        # Validar tamaño
        valid_sizes = ["1024x1024", "1024x1792", "1792x1024"]
        if size not in valid_sizes:
            size = "1024x1024"
            logger.warning(f"Invalid size provided, using default: {size}")

        # Validar imagen de referencia si se proporciona
        if image_0 and image_0.filename:
            logger.info("📸 Using uploaded image as style reference")
            # Validar tipo de archivo
            if not image_0.content_type or not image_0.content_type.startswith("image/"):
                raise HTTPException(
                    status_code=400,
                    detail="El archivo debe ser una imagen válida"
                )

        # Generar múltiples versiones usando el método optimizado de Ideogram (con o sin referencia)
        mode_description = "PRODUCT REFERENCE (incorporando producto)" if use_product_image else "GENERATE (referencia de estilo)"
        logger.info(f"🚀 Generating {num_images} versions with Ideogram batch generation - Mode: {mode_description}")
        result = await ideogram_service.generate_multiple_ads(
            prompt=enhanced_prompt,
            num_images=num_images,
            size=size,
            reference_image=image_0,  # Pass reference image (None if not provided)
            use_product_image=use_product_image  # New parameter to control REMIX vs GENERATE
        )

        if result.get("success"):
            # Crear ID único para el resultado
            result_id = f"free_gen_{int(time.time())}"

            # Preparar respuesta con múltiples imágenes
            response_data = {
                "id": result_id,
                "status": "generated",
                "message": f"🚀 ¡{result.get('num_generated', num_images)} versiones generadas con Emma Enterprise!",
                "image_url": result.get("image_url"),  # Primera imagen como principal
                "all_images": result.get("all_images", [result.get("image_url")] if result.get("image_url") else []),
                "num_generated": result.get("num_generated", 1),
                "revised_prompt": result.get("revised_prompt"),
                "prompt_used": enhanced_prompt,
                "original_prompt": prompt,
                "translated_prompt": english_prompt,
                "platform": platform,
                "size": size,
                "metadata": {
                    "mode": "free_generation_premium",
                    "service": "ideogram",
                    "model": "ideogram-3.0-quality",
                    "timestamp": time.time(),
                    "has_reference_image": bool(image_0 and image_0.filename),
                    "num_requested": num_images,
                    "num_generated": result.get("num_generated", 1),
                    "batch_generation": not bool(image_0 and image_0.filename),
                    "seed": result.get("metadata", {}).get("seed") if result.get("metadata") else None,
                    "style_type": result.get("metadata", {}).get("style_type", "DESIGN") if result.get("metadata") else "DESIGN",
                    "is_image_safe": result.get("metadata", {}).get("is_image_safe", True) if result.get("metadata") else True,
                    "rendering_speed": "QUALITY",
                    "magic_prompt": "AUTO"
                }
            }

            logger.info(f"✅ Successfully generated {response_data['num_generated']} versions")
            return response_data
        else:
            error_message = result.get("error", "Error desconocido en Ideogram")
            logger.error(f"Ideogram generation failed: {error_message}")
            raise HTTPException(
                status_code=500,
                detail=f"Error generando imagen: {error_message}"
            )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error in free generation: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error interno en generación libre: {str(e)}"
        )


@router.post("/generate-field", response_model=GenerateFieldResponse)
async def generate_specific_field(request: GenerateFieldRequest):
    """
    Generate specific field content (headline, punchline, CTA) using the ad creator agent.
    """
    try:
        logger.info(f"Generating {request.field_type} for {request.platform}")
        
        # Validate field type
        valid_fields = ["headline", "punchline", "cta"]
        if request.field_type not in valid_fields:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid field type. Must be one of: {valid_fields}"
            )
        
        # Prepare context for field generation
        context = {
            "platform": request.platform,
            "productDescription": request.product_description,
            "task": f"generate_{request.field_type}"
        }
        
        # Generate the field content
        content = await ad_creator_agent.generate_specific_field(
            field_type=request.field_type,
            context=context
        )
        
        return GenerateFieldResponse(
            content=content,
            field_type=request.field_type,
            platform=request.platform
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating {request.field_type}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating {request.field_type}: {str(e)}"
        )


@router.get("/status")
async def get_ad_creator_agent_status():
    """Get the status of the ad creator agent."""
    try:
        return {
            "status": "online",
            "agent_name": "Emma Ad Creator",
            "capabilities": [
                "chat_interaction",
                "headline_generation",
                "punchline_generation", 
                "cta_generation",
                "description_optimization",
                "platform_specific_advice"
            ],
            "supported_platforms": [
                "facebook",
                "instagram", 
                "linkedin",
                "youtube",
                "google",
                "twitter"
            ],
            "ai_available": ad_creator_agent.ai_provider.is_ai_available()
        }
        
    except Exception as e:
        logger.error(f"Error getting ad creator agent status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting status: {str(e)}"
        )


class SuggestionsRequest(BaseModel):
    """Request model for getting contextual suggestions."""
    platform: str = Field(..., description="Platform for the ad")
    task: str = Field(default="general", description="Current task or context")
    message: str = Field(default="", description="Current user message for context")


@router.post("/suggestions")
async def get_contextual_suggestions(request: SuggestionsRequest):
    """
    Get contextual suggestions for the ad creator interface.
    """
    try:
        suggestions = ad_creator_agent._generate_contextual_suggestions(
            message=request.message,
            platform=request.platform,
            task=request.task
        )

        return {
            "suggestions": suggestions,
            "platform": request.platform,
            "task": request.task
        }

    except Exception as e:
        logger.error(f"Error getting suggestions: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting suggestions: {str(e)}"
        )


# Health check endpoint
class ImprovePromptRequest(BaseModel):
    original_prompt: str = Field(..., description="El prompt original a mejorar")
    context: str = Field(default="advertisement_generation", description="Contexto de uso")
    target_platform: str = Field(default="instagram", description="Plataforma objetivo")

@router.post("/improve-prompt")
async def improve_prompt(request: ImprovePromptRequest):
    """
    Mejora un prompt para generar mejores anuncios.
    Optimiza la descripción para obtener resultados más efectivos.
    """
    try:
        logger.info(f"🧠 Improving prompt: {request.original_prompt[:50]}...")

        # Usar el servicio de AI Provider para mejorar el prompt
        from app.services.ai_provider_service import AIProviderService
        ai_service = AIProviderService()

        improvement_prompt = f"""
        Eres Emma, una experta en marketing digital y creación de anuncios.

        Tu tarea es mejorar este prompt para generar anuncios más efectivos:

        PROMPT ORIGINAL: "{request.original_prompt}"

        CONTEXTO: {request.context}
        PLATAFORMA: {request.target_platform}

        Mejora el prompt siguiendo estas reglas:
        1. Hazlo más específico y detallado
        2. Incluye elementos visuales clave
        3. Menciona beneficios emocionales
        4. Agrega palabras que generen urgencia o deseo
        5. Mantén el idioma original (español)
        6. Máximo 200 palabras
        7. Enfócate en resultados y transformación

        Devuelve SOLO el prompt mejorado, sin explicaciones adicionales.
        """

        improved_prompt = await ai_service.generate_content(improvement_prompt)

        # Limpiar la respuesta
        improved_prompt = improved_prompt.strip().strip('"').strip("'")

        logger.info(f"✅ Prompt improved successfully")

        return {
            "success": True,
            "original_prompt": request.original_prompt,
            "improved_prompt": improved_prompt,
            "message": "Prompt mejorado exitosamente"
        }

    except Exception as e:
        logger.error(f"Error improving prompt: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error mejorando el prompt: {str(e)}"
        )

@router.get("/health")
async def health_check():
    """Health check for the ad creator agent service."""
    return {
        "status": "healthy",
        "service": "ad_creator_agent",
        "timestamp": logger.info("Health check requested")
    }
